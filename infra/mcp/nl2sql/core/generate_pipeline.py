from typing import Dict, List
from datetime import datetime, timezone

import os as os
from common.share import error, config
from infra.mcp.nl2sql.core.basic_fewshot_pipeline import BasicFewshotPipeline
from infra.mcp.nl2sql.core.business_term import BusinessTerm
from infra.mcp.nl2sql.core.lsh import LSH
from infra.mcp.nl2sql.core.os_pipeline import OS
from infra.mcp.nl2sql.core.rsl_pipeline import RSL
from infra.mcp.nl2sql.core.selector_pipeline import SelectorPipeline
from infra.mcp.nl2sql.entities.engine_type_enum import EngineTypeEnum
from infra.mcp.nl2sql.entities.generate_sql_entity import SQLGenerateData, Metadata, DatabaseInfo
from infra.mcp.nl2sql.extract_keywords import extract_keywords
from infra.mcp.nl2sql.llm.llm import embedding_texts
from infra.mcp.nl2sql.preprocessing.es_vector_store import Nl2SQLVectorStore, Nl2sqlSaveEsParam
from infra.mcp.nl2sql.utils.db_util import DatabaseContext, Column, create_database_context
from common.logger.logger import logger


class SQLGenerator:
    """根据自然语言问题生成SQL语句的核心类

    属性:
        metadata (Metadata): 应用元数据信息
        database_info (DatabaseInfo): 数据库连接和结构信息
        question (str): 用户提出的自然语言问题
    """
    metadata: Metadata
    database_info: DatabaseInfo
    question: str

    def __init__(self, question: str, metadata: Metadata, database_info: DatabaseInfo):
        """初始化SQL生成器实例

        参数:
            question (str): 需要转换为SQL的自然语言问题
            metadata (Metadata): 包含应用追踪信息的元数据对象
            database_info (DatabaseInfo): 数据库连接和结构信息对象

        异常:
            ValueError: 当输入参数为空时抛出
        """
        # 参数有效性校验
        if not question or metadata is None or database_info is None:
            logger.error(
                "SQL生成器初始化参数为空 问题: %s, 元数据: %s, 数据库信息: %s",
                question, metadata, database_info
            )
            raise ValueError(error.ErrorCode.ParamError.value)

        # 初始化实例属性
        self.metadata = metadata
        self.database_info = database_info
        self.question = question

    def generate(self) -> SQLGenerateData:

        keywords = extract_keywords(self.question,self.metadata.trace_id)
        logger.info(f"Extracting keywords from {self.question},keywords: {keywords},trace_id: {self.metadata.trace_id}")
        database_context = self._get_database_context()
        ddl = self._get_tables_ddl(database_context)
        logger.info(f"Get tables ddl: {ddl},trace_id: {self.metadata.trace_id}")
        if ddl is None or len(ddl) < 1:
            logger.error(f"未找到数据表，datasource_name = {self.database_info.datasource_name}, "
                         f"dbname = {self.database_info.dbname},tables = {self.database_info.tables},"
                         f"trace_id:{self.metadata.trace_id}")
            raise ValueError(error.ErrorCode.NotFoundError.value)

        db_schema = self._get_tables_schema(database_context, ddl)
        # 更新表数据，因为 es 可能存在 pattern 模式例如 index_* 可能拿到多个索引
        self.database_info.tables = db_schema.keys()
        lsh = None
        if self.database_info.isSampling:
            lsh = self._init_lsh(database_context)
            logger.info(f"Initialize lsh finished ,trace_id:{self.metadata.trace_id}")

        es_config = config.appConfig.automic.nl2sql.es
        emb_model = config.appConfig.automic.nl2sql.embedding
        vector_store = Nl2SQLVectorStore(es_config, self.metadata.trace_id, emb_model.model_dims, self.metadata.app_id)

        # 调用 os pipeline
        sqls = self._run_pipelines(lsh, keywords, db_schema, ddl, vector_store)

        final_sql = self._select_sql(lsh, sqls, ddl, keywords, db_schema)
        logger.info(f"Generate sql finished, final_sql rsp {final_sql}, trace_id:{self.metadata.trace_id}")
        self._save_record_to_es(vector_store, final_sql, emb_model.model_name, emb_model.model_dims)
        return final_sql

    def _get_database_context(self) -> DatabaseContext:
        """获取数据库连接上下文"""
        return create_database_context(
            self.database_info.engine_type,
            self.database_info.mcp_url
        )

    def _get_tables_ddl(self, database_context: DatabaseContext) -> Dict[str, str]:
        return database_context.get_tables_ddl(self.database_info.tables, self.database_info.dbname,
                                               self.database_info.datasource_name)

    def _get_tables_schema(self, database_context: DatabaseContext, ddl: Dict[str, str]) -> Dict[str, List[Column]]:
        if self.database_info.engine_type.lower() == EngineTypeEnum.TC_HOUSE_D.value.lower():
            return database_context.get_tables_schema({
                "Catalog": self.database_info.datasource_name,
                "Database": self.database_info.dbname,
                "Tables": self.database_info.tables
            })
        elif self.database_info.engine_type.lower() == EngineTypeEnum.DLC.value.lower():
            return database_context.get_tables_schema(params=ddl)
        elif self.database_info.engine_type.lower() == EngineTypeEnum.ES.value.lower():
            return database_context.get_tables_schema(params={
                "Tables": self.database_info.tables
            })
        elif self.database_info.engine_type.lower() == EngineTypeEnum.TC_HOUSE_C.value.lower():
            return database_context.get_tables_schema(params={
                "Database": self.database_info.dbname,
                "Tables": self.database_info.tables
            })
        return {}

    def _init_lsh(self, database_context: DatabaseContext) -> LSH:
        lsh = LSH(signature_size=100, n_gram=3, threshold=0.01, tables=self.database_info.tables,
                  db_name=self.database_info.dbname, engine_name=self.database_info.engine_name,
                  datasource_name=self.database_info.datasource_name,
                  database_context=database_context, trace_id=self.metadata.trace_id, app_id=self.metadata.app_id,
                  sub_account_uin=self.metadata.sub_account_uin, engine_type=self.database_info.engine_type)
        lsh.create_lsh()
        return lsh

    def _save_record_to_es(self, vector_store, final_sql: SQLGenerateData, model_name: str, model_dims: int):
        logger.info(f"Generate sql finished, start save record to es,trace_id:{self.metadata.trace_id}")
        if not final_sql.sql:
            logger.warning(f"没有生成有效的 sql,reasoning:{final_sql.reasoning}")
            return

        try:
            question_emb = []
            if self.question:
                question_embeddings = embedding_texts([self.question])
                if not question_embeddings:  # 空数组判断
                    logger.warning(
                        f"问题嵌入返回空数组，问题内容：'{self.question[:50]}'，trace_id={self.metadata.trace_id}")
                else:
                    question_emb = question_embeddings[0]
            sql_emb = []
            if final_sql.sql:
                sql_embeddings = embedding_texts([final_sql.sql])
                if not sql_embeddings:  # 空数组判断
                    logger.warning(f"SQL嵌入返回空数组，SQL内容：'{final_sql.sql[:50]}'，trace_id={self.metadata.trace_id}")
                else:
                    sql_emb = sql_embeddings[0]

            doc_id = vector_store.save_nl2sql_record(Nl2sqlSaveEsParam(
                app_id=self.metadata.app_id,
                sub_account_uin=self.metadata.sub_account_uin,
                trace_id=self.metadata.trace_id,
                record_id=self.metadata.record_id,
                datasource_name=self.database_info.datasource_name,
                dbname=self.database_info.dbname,
                engine=self.database_info.engine_name,
                engine_type=self.database_info.engine_type,  # 修复赋值运算符
                question=self.question,
                sql=final_sql.sql,
                embedding_model=model_name,
                embedding_dims=model_dims,  # 修复冒号错误
                db_schema=final_sql.db_schema,
                vector_question=question_emb,
                vector_sql=sql_emb,
                create_time=datetime.now(timezone.utc),
                update_time=datetime.now(timezone.utc),
                tables=self.database_info.tables,
            ))
            logger.info(f"Save record to es success,doc_id:{doc_id},trace_id:{self.metadata.trace_id}")
        except Exception as e:
            logger.warning(f"Save record to es failed trace_id:{self.metadata.trace_id},e{e}")

    def _select_sql(self, lsh: LSH, candidates: [SQLGenerateData], ddl: Dict[str, str], keywords: List[str],
                    db_schema: Dict[str, List[Column]]) -> SQLGenerateData:
        candidate_sqls = [c.sql for c in candidates if c.sql and c.sql.strip()]
        if len(candidate_sqls) == 0:
            return candidates[0]
        selector_pipeline = SelectorPipeline(lsh=lsh, candidates=candidate_sqls, question=self.question, ddl=ddl,
                                             keywords=keywords,
                                             db_schema=db_schema, trace_id=self.metadata.trace_id)
        final_sql = selector_pipeline.select_final_sql()
        # 使用生成器表达式优化查找效率，直接返回匹配项
        matched = next((c for c in candidates if c.sql == final_sql), None)
        if matched:
            return matched
        return SQLGenerateData(sql=final_sql, reasoning="", tables=[],
                               db_schema="\n".join([f"{k}: {v}" for k, v in ddl.items()]))

    def _run_pipelines(self, lsh: LSH, keywords: List[str], db_schema: Dict[str, List[Column]], ddl: Dict[str, str],
                       vector_store: Nl2SQLVectorStore) -> []:
        """并行执行所有SQL生成pipeline

        参数:
            lsh: LSH实例
            keywords: 关键词列表
            db_schema: 数据库模式
            ddl: 数据定义语句
            vector_store: 向量存储实例

        返回:
            []: [os_sql, rsl_sql, basic_sql] 三个pipeline的结果
        """

        # 业务术语处理(初始化)
        business_term = self._init_business_term(vector_store, keywords)

        # 获取业务术语内容
        business_terms_section = business_term.get_business_terms_section()

        from concurrent.futures import ThreadPoolExecutor, as_completed

        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = {
                executor.submit(
                    lambda cls, args: cls(*args).run(),
                    OS,
                    (lsh, self.question, keywords, db_schema, ddl, self.metadata.trace_id, vector_store,
                     self.metadata.app_id, self.database_info.datasource_name, self.database_info.dbname,
                     self.database_info.engine_type, business_terms_section)
                ): "os_pipeline",

                executor.submit(
                    lambda cls, args: cls(*args).run(),
                    RSL,
                    (lsh, self.question, keywords, db_schema, ddl, self.metadata.trace_id, vector_store,
                     self.metadata.app_id, self.database_info.datasource_name, self.database_info.dbname,
                     self.database_info.engine_type, business_terms_section)
                ): "rsl_pipeline",

                executor.submit(
                    lambda cls, args: cls(*args).run(),
                    BasicFewshotPipeline,
                    (lsh, self.question, keywords, db_schema, ddl, self.metadata.trace_id, vector_store,
                     self.metadata.app_id, self.database_info.datasource_name, self.database_info.dbname,
                     self.database_info.engine_type, business_terms_section)
                ): "basic_pipeline"
            }

            results = {}
            for future in as_completed(futures):
                pipeline_name = futures[future]
                try:
                    results[pipeline_name] = future.result()
                    logger.info(
                        f"Generate sql finished, {pipeline_name} rsp {results[pipeline_name]}, trace_id:{
                        self.metadata.trace_id}")
                except Exception as e:
                    logger.warning(f"{pipeline_name}执行失败: {str(e)}, trace_id:{self.metadata.trace_id}")
                    results[pipeline_name] = SQLGenerateData(sql="", reasoning="", tables=[], db_schema="")

            return [
                results.get("os_pipeline", SQLGenerateData(sql="", reasoning="", tables=[], db_schema="")),
                results.get("rsl_pipeline", SQLGenerateData(sql="", reasoning="", tables=[], db_schema="")),
                results.get("basic_pipeline", SQLGenerateData(sql="", reasoning="", tables=[], db_schema=""))
            ]

    def _init_business_term(self, vector_store: Nl2SQLVectorStore, keywords: List[str]) -> BusinessTerm:
        """初始化业务术语处理类"""
        logger.info(f"初始化业务术语处理, trace_id={self.metadata.trace_id}")

        business_term = BusinessTerm(
            app_id=self.metadata.app_id,
            user_id=self.metadata.sub_account_uin,
            trace_id=self.metadata.trace_id,
            datasource_name=self.database_info.datasource_name,
            dbname=self.database_info.dbname,
            engine_type=self.database_info.engine_type,
            vector_store=vector_store,
            tables=self.database_info.tables
        )

        business_term.process_business_terms(keywords)
        return business_term


if __name__ == '__main__':
    os.environ["OPENAI_API_BASE"] = "http://v2.open.venus.oa.com/llmproxy"
    os.environ["OPENAI_API_KEY"] = "ZrNq18iMTvHDwyv5zqB5x4lw@2739"
    test_metadata = Metadata(user_id="collinsdeng", app_id="1", trace_id="collinsdeng", record_id="1")
    test_database_info = DatabaseInfo(engine="data-agent-exp-dev", datasource_name="DataLakeCatalog",
                                      dbname="test_samuelzan",
                                      tables=["test_samuelzan_forcast"], is_sampling=True,
                                      mcp_url={"dlc": "http://127.0.0.1:31234/sse?auth_token=SECRET_KEY_2025"},
                                      engine_type="dlc")
    test_question = "价格大于 1.5 小于 1.8 的的货物地址是哪里"
    generate = SQLGenerator(metadata=test_metadata, database_info=test_database_info, question=test_question)
    sql = generate.generate()
    print(f"sql={sql.sql},reasoning = {sql.reasoning},tables = {sql.tables}")
