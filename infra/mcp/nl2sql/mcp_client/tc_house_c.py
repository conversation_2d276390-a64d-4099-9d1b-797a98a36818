import json
from typing import Any, List

from mcp import types
from pydantic import BaseModel

from common.logger.logger import logger
from infra.mcp.nl2sql.mcp_client.client import call_mcp_tool

from infra.mcp.nl2sql.mcp_client.common_entity import Column, Table


class GetTCHouseCGetTablesSchemaReq(BaseModel):
    Database: str
    Tables: List[str]


class TCHouseCTableSchemas(BaseModel):
    Table: str
    CreateSql: str


class GetTCHouseCGetTablesSchemaRsp(BaseModel):
    RequestId: str
    Database: str
    Tables: List[TCHouseCTableSchemas]
    NotExistTable: list[str]

    @classmethod
    def default(cls) -> "GetTCHouseCGetTablesSchemaRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            RequestId="",
            Database="",
            Tables=[],
            NotExistTable=[]
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "GetTCHouseCGetTablesSchemaRsp":
        if not raw_result or not raw_result[0]:
            logger.warning(f"查询数据库 schema 获取 mcp text content 内容为空")
            return GetTCHouseCGetTablesSchemaRsp.default()
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return GetTCHouseCGetTablesSchemaRsp.default()
        # 解析第一段文本内容中的JSON响应
        try:
            tables_ddl_json = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"tc house c 数据库 schema JSON解析失败,text: {content["text"]},error:{e}")
            return GetTCHouseCGetTablesSchemaRsp.default()

        if tables_ddl_json is None:
            logger.warning(f"查询数据库 schema 获取 mcp text 内容为空")
            return GetTCHouseCGetTablesSchemaRsp.default()
        return cls.model_validate({
            "RequestId": tables_ddl_json.get("RequestId") or "",
            "Database": tables_ddl_json.get("Database") or "",
            "Tables": [  # 结构化结果字段元数据
                TCHouseCTableSchemas(
                    Table=ddl.get("Table") or "",
                    CreateSql=ddl.get("CreateSql") or ""
                ) for ddl in tables_ddl_json.get("TableSchemas") or []
            ],
            "NotExistTable": tables_ddl_json.get("NotExistTable") or [],
        })


class TCHouseCSampleDataReq(BaseModel):
    Database: str
    Table: str
    Limit: int


class TCHouseCSampleDataRsp(BaseModel):
    RequestId: str
    Database: str
    Table: str
    ResultSchema: list[str]  # 字段
    ResultSet: list[list[Any]]  # 数据

    @classmethod
    def default(cls) -> "TCHouseCSampleDataRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            RequestId="",
            Database="",
            Table="",
            ResultSchema=[],
            ResultSet=[]
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "TCHouseCSampleDataRsp":
        if not raw_result or not raw_result[0]:
            logger.warning("tc house c 采样数据 mcp text content 返回为空")
            return TCHouseCSampleDataRsp.default()
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return TCHouseCSampleDataRsp.default()
        # 解析第一段文本内容中的JSON响应
        try:
            mcp_rsp_json = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"tc house c 采样数据JSON解析失败,text: {content["text"]},error:{e}")
            return TCHouseCSampleDataRsp.default()
        if mcp_rsp_json is None:
            logger.warning("tc house c 采样数据 mcp text 为空")
            return TCHouseCSampleDataRsp.default()
        sample_data = mcp_rsp_json.get("SampleData") or []
        if len(sample_data) == 0:
            logger.warning(f"tc house c 采样数据为空,"
                           f"Database:{mcp_rsp_json.get("Database")},Table:{mcp_rsp_json.get("Table")},"
                           f"request_id:{mcp_rsp_json.get("RequestId")}")
            return TCHouseCSampleDataRsp.default()

        columns = list(sample_data[0].keys()) if sample_data else []
        # 按固定键顺序提取所有值
        values = [
            [item[key] for key in columns]  # 使用固定键顺序保证一致性
            for item in sample_data
        ]
        return cls.model_validate({
            "RequestId": mcp_rsp_json.get("RequestId") or "",
            "Database": mcp_rsp_json.get("Database") or "",
            "Table": mcp_rsp_json.get("Table") or "",
            "ResultSchema": columns,
            "ResultSet": values,
        })


class TCHouseCListTablesReq(BaseModel):
    Database: str
    Tables: List[str]


class TCHouseCListTableRsp(BaseModel):
    RequestId: str
    Database: str
    Tables: List[Table]

    @classmethod
    def default(cls) -> "TCHouseCListTableRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            RequestId="",
            Database="",
            Tables=[]
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "TCHouseCListTableRsp":
        if not raw_result or not raw_result[0]:
            logger.warning("tc house c 获取表详细信息 mcp text content 返回为空")
            return TCHouseCListTableRsp.default()
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return TCHouseCListTableRsp.default()
        # 解析第一段文本内容中的JSON响应
        try:
            mcp_rsp_json = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"tc house c 获取表详细信息JSON解析失败,text: {content["text"]},error:{e}")
            return TCHouseCListTableRsp.default()
        if mcp_rsp_json is None:
            logger.warning("tc house c 获取表详细信息 mcp text 为空")
            return TCHouseCListTableRsp.default()
        table_details = mcp_rsp_json.get("TableDetails") or []

        if len(table_details) == 0:
            logger.warning(f"tc house c 获取表详细信息为空,"
                           f"Database:{mcp_rsp_json.get("Database")},"
                           f"request_id:{mcp_rsp_json.get("RequestId")}")
            return TCHouseCListTableRsp.default()
        tables = []
        for table_detail in table_details:
            columns = []
            for column in table_detail.get("Columns", []):
                columns.append(Column(Name=column.get("Name"), Type=column.get("Type"), Comment=column.get("Comment")))
            tables.append(Table(Name=table_detail.get("TableName") or "",
                                TableComment=table_detail.get("BasicInfo", {}).get("Comment") or "",
                                Columns=columns))

        return cls.model_validate({
            "RequestId": mcp_rsp_json.get("RequestId") or "",
            "Database": mcp_rsp_json.get("Database") or "",
            "Tables": tables,
        })


class TCHouseCListTableNames(BaseModel):
    Database: str


class TCHouseCListTableNameRsp(BaseModel):
    RequestId: str
    Database: str
    TableNames: List[str]

    @classmethod
    def default(cls) -> "TCHouseCListTableNameRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            RequestId="",
            Database="",
            TableNames=[]
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "TCHouseCListTableNameRsp":
        if not raw_result or not raw_result[0]:
            logger.warning("tc house c 获取表名信息 mcp text content 返回为空")
            return TCHouseCListTableNameRsp.default()
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return TCHouseCListTableNameRsp.default()
        # 解析第一段文本内容中的JSON响应
        try:
            mcp_rsp_json = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"tc house c 获取表名信息JSON解析失败,text: {content["text"]},error:{e}")
            return TCHouseCListTableNameRsp.default()
        if mcp_rsp_json is None:
            logger.warning("tc house c 获取表名信息 mcp text 为空")
            return TCHouseCListTableNameRsp.default()
        table_names = mcp_rsp_json.get("TableNames") or []

        if len(table_names) == 0:
            logger.warning(f"tc house c 获取表名为空,"
                           f"Database:{mcp_rsp_json.get("Database")},"
                           f"request_id:{mcp_rsp_json.get("RequestId")}")
            return TCHouseCListTableNameRsp.default()

        return cls.model_validate({
            "RequestId": mcp_rsp_json.get("RequestId") or "",
            "Database": mcp_rsp_json.get("Database") or "",
            "TableNames": table_names,
        })


def tc_house_c_get_tables_schema(url: str, arguments: GetTCHouseCGetTablesSchemaReq,
                                 tool_name: str = "TCHouseCGetTableSchema") -> "GetTCHouseCGetTablesSchemaRsp":
    params = arguments.model_dump()
    logger.info(f"Start request tc-house-c get schema tool, params={params}")
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return GetTCHouseCGetTablesSchemaRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 tc-house-c get schema tool mcp 工具，响应解析失败，原始内容:{mcp_result.content}",
                     exc_info=True)
        raise e


def tc_house_c_sample_data(url: str, arguments: TCHouseCSampleDataReq,
                           tool_name: str = "TCHouseCGetTableSample") -> "TCHouseCSampleDataRsp":
    params = arguments.model_dump()
    logger.info(f"Start request tc-house-c sample_data tool, params={params}")
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return TCHouseCSampleDataRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 tc-house-c sample data tool mcp 工具，响应解析失败 原始内容:{mcp_result.content}",
                     exc_info=True)
        raise e


def tc_house_c_list_tables(url: str, arguments: TCHouseCListTablesReq,
                           tool_name: str = "TCHouseCListTableInfos") -> "TCHouseCListTableRsp":
    params = arguments.model_dump()
    logger.info(f"Start request tc-house-c list_tables tool, params={params}")
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return TCHouseCListTableRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 tc-house-c list tables tool mcp 工具 响应解析失败，原始内容:{mcp_result.content}",
                     exc_info=True)
        raise e


def tc_house_c_list_table_names(url: str, arguments: TCHouseCListTableNames,
                                tool_name: str = "TCHouseCListTableNames") -> "TCHouseCListTableNameRsp":
    params = arguments.model_dump()
    logger.info(f"Start request tc-house-c list_table_names tool, params={params}")
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return TCHouseCListTableNameRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 tc-house-c list table names tool mcp 工具 响应解析失败，原始内容:{mcp_result.content}",
                     exc_info=True)
        raise e
