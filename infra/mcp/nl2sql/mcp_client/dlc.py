import json
from typing import List, Any

from mcp import types
from pydantic import BaseModel

from common.logger.logger import logger
from infra.mcp.nl2sql.mcp_client.client import call_mcp_tool

from infra.mcp.nl2sql.mcp_client.common_entity import Column, TableBaseInfo


class DLCExecuteQueryReq(BaseModel):
    """DLC查询请求参数模型

    用于封装通过数据湖计算服务（DLC）执行查询时所需的参数

    Attributes:
        SparkSQL (str): Spark SQL查询语句及配置信息（可选，与SQL参数至少需传一个，优先使用本参数）
        DatabaseName (str): 默认数据库名称（当SQL语句未指定数据库时生效）
        DatasourceConnectionName (str): 数据源连接名称（默认值：DataLakeCatalog）
        DataEngineName (str): 数据引擎名称（必填参数）
    """
    SparkSQL: str
    DatabaseName: str
    DatasourceConnectionName: str
    DataEngineName: str


class DLCListTablesReq(BaseModel):
    DatabaseName: str
    DatasourceConnectionName: str
    TableNames: List[str]


class DLCListTablesRsp(BaseModel):
    TableBaseInfo: TableBaseInfo
    Columns: List[Column]

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "List[DLCListTablesRsp]":
        """解析原始响应数据并构建响应模型

        从MCP服务返回的原始SSE响应中提取关键字段，处理嵌套数据结构，
        并转换为强类型的Pydantic模型

        Args:
            raw_result (List[types.TextContent]): MCP服务返回的原始文本内容列表，
                通常包含JSON格式的响应数据

        Returns:
            DLCExecuteQueryRsp: 结构化的响应模型实例

        Raises:
            JSONDecodeError: 当响应内容不是合法JSON时抛出异常
        """
        if not raw_result or not raw_result[0]:
            return []
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return []
        # 解析第一段文本内容中的JSON响应
        try:
            response = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"dlc 查询数据库 schema JSON解析失败,text: {content["text"]},error:{e}")
            return []
        table_list = response.get("Response", {}).get("TableList", [])
        if table_list is None:
            logger.error(f"查询数据库 schema 获取内容为空，request_id={response.get("RequestId", "")}")
            return []
        tables = []
        for table in table_list:
            t = cls.model_validate({
                "TableBaseInfo": table.get("TableBaseInfo", {}),
                "Columns": table.get("Columns", []),
            })
            tables.append(t)
        return tables


class DLCExecuteQueryRsp(BaseModel):
    """DLC查询执行响应模型

    封装数据湖计算服务（DLC）执行查询后的响应数据结构

    Attributes:
        TaskId (str): 任务唯一标识（DLC系统生成的UUID）
        SQL (str): 实际执行的Spark SQL语句
        State (int): 任务状态码（0-成功，1-运行中，2-失败）
        ResultSchema (list[Column]): 结果字段元数据列表，包含名称/类型/注释
        ResultSet (list[list[Any]]): 二维数组形式的查询结果，外层列表代表行，内层列表代表列值
        RequestId (str): 请求唯一标识（用于链路追踪）
    """
    TaskId: str  # 任务唯一标识
    SQL: str  # 执行的SQL语句
    State: int  # 任务状态码
    ResultSchema: list[Column]  # 结果集结构定义
    ResultSet: list[list[Any]]  # 查询结果数据
    RequestId: str

    @classmethod
    def default(cls) -> "DLCExecuteQueryRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            TaskId="",
            SQL="",
            State=-1,  # 使用-1表示无效状态
            ResultSchema=[],
            ResultSet=[],
            RequestId=""
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "DLCExecuteQueryRsp":
        """解析原始响应数据并构建响应模型

        从MCP服务返回的原始SSE响应中提取关键字段，处理嵌套数据结构，
        并转换为强类型的Pydantic模型

        Args:
            raw_result (List[types.TextContent]): MCP服务返回的原始文本内容列表，
                通常包含JSON格式的响应数据

        Returns:
            DLCExecuteQueryRsp: 结构化的响应模型实例

        Raises:
            JSONDecodeError: 当响应内容不是合法JSON时抛出异常
        """
        if not raw_result or not raw_result[0]:
            return DLCExecuteQueryRsp.default()
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return DLCExecuteQueryRsp.default()
        # 解析第一段文本内容中的JSON响应
        try:
            response = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"dlc 执行 sql 查询 JSON解析失败,text: {content["text"]},error:{e}")
            return DLCExecuteQueryRsp.default()

        # 提取嵌套的TaskInfo字段
        task_info = response.get("Response", {}).get("TaskInfo", {})
        # 转换字符串格式的ResultSet（DLC接口可能返回JSON字符串）
        if isinstance(task_info.get("ResultSet"), str):
            task_info["ResultSet"] = json.loads(task_info["ResultSet"])
        # 通过Pydantic模型进行数据校验和转换
        return cls.model_validate({
            "TaskId": task_info.get("TaskId", ""),  # 从TaskInfo提取任务ID
            "SQL": task_info.get("SQL", ""),  # 获取实际执行的SQL语句
            "State": task_info.get("State", -1),  # 任务状态码（0-成功，1-运行中，2-失败）
            "ResultSchema": [  # 结构化结果字段元数据
                Column(
                    Name=col.get("Name", ""),
                    Type=col.get("Type", ""),
                    Comment=col.get("Comment", "")
                ) for col in task_info.get("ResultSchema", [])
            ],
            "ResultSet": task_info.get("ResultSet", []),  # 二维数组形式的结果集
            "RequestId": response.get("Response", {}).get("RequestId", "")  # 从根响应获取请求ID
        })


class DLCListTableNamesReq(BaseModel):
    DatabaseName: str
    DatasourceConnectionName: str
    Limit: int
    Offset: int


class DLCListTableNamesRsp(BaseModel):
    RequestId: str
    TableNames: List[str]

    @classmethod
    def default(cls) -> "DLCListTableNamesRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            RequestId="",
            Tables=[]
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "DLCListTableNamesRsp":
        """解析原始响应数据并构建响应模型

        从MCP服务返回的原始SSE响应中提取关键字段，处理嵌套数据结构，
        并转换为强类型的Pydantic模型

        Args:
            raw_result (List[types.TextContent]): MCP服务返回的原始文本内容列表，
                通常包含JSON格式的响应数据

        Returns:
            DLCListTableNamesRsp: 结构化的响应模型实例

        Raises:
            JSONDecodeError: 当响应内容不是合法JSON时抛出异常
        """
        if not raw_result or not raw_result[0]:
            return cls.default()
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return cls.default()
        # 解析第一段文本内容中的JSON响应
        try:
            response = json.loads(content["text"])
        except json.JSONDecodeError as e:
            logger.error(f"dlc 查询数据库表名 JSON解析失败,text: {content["text"]},error:{e}")
            return cls.default()
        table_names = response.get("Response", {}).get("TableNames", [])
        if table_names is None:
            logger.warn(f"查询数据库表名 获取内容为空，request_id={response.get("RequestId", "")}")
            return cls.default()
        return cls.model_validate({
            "RequestId": response.get("Response", {}).get("RequestId", ""),
            "TableNames": table_names,
        })


def dlc_execute_query(url: str, arguments: DLCExecuteQueryReq,
                      tool_name: str = "DLCExecuteQuery") -> DLCExecuteQueryRsp:
    """执行DLC查询任务（同步封装异步调用）

    通过MCP服务调用数据湖计算（DLC）的Spark SQL查询接口，使用同步方式封装底层异步SSE通信协议

    Args:
        url (str): MCP服务器SSE连接地址（格式示例：http://host:port/sse?auth_token=xxx）
        arguments (DLCExecuteQueryReq): 查询请求参数对象，包含SparkSQL语句/数据库/数据源等配置
        tool_name (str, optional): MCP服务注册的工具名称，默认为"DLCExecuteQuery"

    Returns:
        DLCExecuteQueryRsp: 结构化查询响应对象，包含任务状态/结果集/元数据等信息

    Raises:
        ValueError: 当输入参数验证失败时抛出
        RuntimeError: 当SSE通信或服务端返回错误时抛出
    """
    # 将Pydantic模型参数转换为字典格式（兼容MCP接口参数规范）
    params = arguments.model_dump()
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return DLCExecuteQueryRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 dlc execute query mcp 工具，响应解析失败，原始内容={mcp_result.content}", exc_info=True)
        raise e


def dlc_list_tables(url: str, arguments: DLCListTablesReq, tool_name: str = "DLCListTables") -> List[DLCListTablesRsp]:
    params = arguments.model_dump()
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return DLCListTablesRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 dlc list tables mcp 工具，响应解析失败，原始内容:{mcp_result.content}", exc_info=True)
        raise e


def dlc_list_table_names(url: str, arguments: DLCListTableNamesReq, tool_name: str = "DLCListTableNames") \
        -> "DLCListTableNamesRsp":
    params = arguments.model_dump()
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return DLCListTableNamesRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 dlc list table names mcp 工具，响应解析失败，原始内容:{mcp_result.content}", exc_info=True)
        raise e


if __name__ == '__main__':
    sql1 = "select * from orders"
    sql12 = "show create table test_samuelzan_forcast"
    sql2 = "SELECT `address` FROM `test_samuelzan_forcast` WHERE `price` = 1.6"
    param = DLCExecuteQueryReq(SparkSQL=sql1, DatabaseName="nl2sql_test",
                               DatasourceConnectionName="DataLakeCatalog",
                               DataEngineName="data-agent-exp-dev")
    dlc_list_tables_param = DLCListTablesReq(DatabaseName="nl2sql_test",
                                             DatasourceConnectionName="DataLakeCatalog",
                                             TableNames=["orders", "products", "customers", "test"])
    rsp = dlc_list_tables("http://127.0.0.1:31234/sse?auth_token=SECRET_KEY_2025", dlc_list_tables_param)
    print(rsp)
    result = dlc_execute_query("http://127.0.0.1:31234/sse?auth_token=SECRET_KEY_2025", param)
    print(result)
