import regex as re
from infra.mcp.codegen.nl2code.llm import llm_chat, llm_for_openai
from infra.mcp.codegen.nl2code.lib_config_manager import apply_library_config_fixes, get_library_config_report
from infra.mcp.codegen.nl2code.scenario_manager import scenario_manager, DataScienceScenario, detect_scenario
from infra.mcp.codegen.nl2code.action_manager import PreviousActionManager, ActionManagerConfig
from typing import Dict, Any, Optional, List, Tuple
from common.share.config import appConfig
from pydantic import BaseModel, ValidationError, Field


def filter_plt_font_code(code: str) -> str:
    """
    Filter out matplotlib font-related code to prevent font warnings.
    Removes lines that set font families or font names, but preserves size, weight, and style settings.
    """
    try:
        lines = code.split('\n')
        filtered_lines = []
        
        for line in lines:
            # Skip lines that contain font family or font name settings only
            if any(pattern in line.lower() for pattern in [
                'rcparams[\'font.', 'rcparams["font."',
                '.set_fontfamily(',
                '.set_fontname(',
                'sans-serif',
                'fontfamily=',
                'font_family=',
                'family=',
                'fontname=',
                'font_name=',
                'font.family',
                'font.name',
                'plt.rc(\'font.\'', 'plt.rc("font."',
                'matplotlib.rc(\'font.\'', 'matplotlib.rc("font."',
                'rc(\'font.\'', 'rc("font."',
            ]) and not any(pattern in line.lower() for pattern in [
                '.size', '.weight', '.style'
            ]):
                # Add comment explaining why line was filtered
                filtered_lines.append(f"# Filtered font setting: {line.strip()}")
            else:
                filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)
    except Exception as e:
        return f"# Error filtering font code: {str(e)}\n{code}"


def add_data_sampling_for_plots(code: str) -> str:
    """
    Add automatic data sampling for visualization when dealing with large datasets.
    Inserts sampling logic before plot commands to limit data for better readability.
    """
    try:
        lines = code.split('\n')
        processed_lines = []
        plot_detected = False
        
        for i, line in enumerate(lines):
            # Detect plot commands  
            is_plot_line = any(plot_cmd in line.lower() for plot_cmd in [
                '.plot(', '.bar(', '.barh(', '.scatter(', '.hist(', 
                'plt.plot(', 'plt.bar(', 'plt.barh(', 'plt.scatter(', 'plt.hist(',
                'sns.', 'seaborn.', '.boxplot(', '.violinplot(', 'plt.pie('
            ])
            
            if is_plot_line:
                plot_detected = True
                
                # Look for dataframe variable in the current and previous lines
                df_var = None
                search_lines = lines[max(0, i-3):i+1]  # Look at current and 3 previous lines
                
                # Check current line first
                import re
                
                # Method 1: DataFrame method calls like df.plot() (but not plt.plot)
                df_method_match = re.search(r'(\w+)\.(?:plot|bar|barh|scatter|hist|boxplot|violinplot)', line)
                if df_method_match:
                    potential_df_var = df_method_match.group(1)
                    # Only exclude known non-dataframe objects
                    excluded_vars = {'ax', 'fig', 'plt', 'matplotlib', 'model', 'forecast', 'future'}
                    if potential_df_var not in excluded_vars:
                        df_var = potential_df_var
                
                # Method 2: Seaborn with data= parameter
                elif 'sns.' in line or 'seaborn.' in line:
                    sns_match = re.search(r'data\s*=\s*(\w+)', line)
                    if sns_match:
                        potential_df_var = sns_match.group(1)
                        # Apply same DataFrame detection logic
                        excluded_vars = {'ax', 'fig', 'plt', 'matplotlib', 'model', 'forecast', 'future', 'results'}
                        if potential_df_var not in excluded_vars:
                            df_var = potential_df_var
                
                # Method 3: Matplotlib functions with DataFrame as first argument  
                if 'plt.' in line and not df_var:
                    # Look for patterns like plt.plot(df, ...) or plt.plot(df['col'], ...)
                    plt_match = re.search(r'plt\.\w+\(\s*(\w+)(?:\[|\.|,|\))', line)
                    if plt_match:
                        potential_var = plt_match.group(1)
                        # Skip common non-dataframe variables and model objects
                        excluded_vars = {'x', 'y', 'ax', 'fig', 'color', 'label', 'alpha', 'plt', 'matplotlib', 
                                       'model', 'forecast', 'future', 'np', 'pd', 'sns', 
                                       'seaborn', 'prophet', 'Prophet', 'mean_absolute_error', 'mean_squared_error'}
                        if potential_var not in excluded_vars:
                            df_var = potential_var
                    else:
                        # Also look for DataFrame column access patterns like df['column']
                        column_match = re.search(r'plt\.\w+\([^,]*(\w+)\[', line)
                        if column_match:
                            potential_var = column_match.group(1)
                            excluded_vars = {'x', 'y', 'ax', 'fig', 'color', 'label', 'alpha', 'plt', 'matplotlib', 
                                           'model', 'forecast', 'future', 'np', 'pd', 'sns', 
                                           'seaborn', 'prophet', 'Prophet', 'mean_absolute_error', 'mean_squared_error'}
                            if potential_var not in excluded_vars:
                                df_var = potential_var
                
                # Add sampling logic if DataFrame variable is found
                if df_var and not any('head(' in prev_line or 'tail(' in prev_line or 'sample(' in prev_line 
                                    for prev_line in lines[max(0, i-5):i]):
                    processed_lines.append(f"# Auto-sampling for better visualization readability")
                    # Use safer condition for known problematic cases, simpler for others
                    if df_var in ['model', 'forecast', 'future', 'ax', 'fig', 'plt']:
                        processed_lines.append(f"if hasattr({df_var}, 'head') and hasattr({df_var}, '__len__') and len({df_var}) > 15:")
                    else:
                        processed_lines.append(f"if len({df_var}) > 15:")
                    processed_lines.append(f"    {df_var}_plot = {df_var}.head(15)  # Show top 15 rows")
                    processed_lines.append(f"    print(f'Note: Showing top 15 out of {{len({df_var})}} rows for better readability')")
                    processed_lines.append(f"else:")
                    processed_lines.append(f"    {df_var}_plot = {df_var}")
                    processed_lines.append("")
                    
                    # Modify the plot line to use sampled data
                    if f'{df_var}.' in line:
                        # Handle DataFrame method calls like df.plot()
                        modified_line = line.replace(f'{df_var}.', f'{df_var}_plot.')
                    elif f'data={df_var}' in line:
                        # Handle seaborn data= parameter
                        modified_line = line.replace(f'data={df_var}', f'data={df_var}_plot')
                    elif f'data = {df_var}' in line:
                        # Handle seaborn data = parameter with spaces
                        modified_line = line.replace(f'data = {df_var}', f'data = {df_var}_plot')
                    else:
                        # Handle other cases like plt.plot(df, ...)
                        modified_line = line.replace(df_var, f'{df_var}_plot', 1)
                    processed_lines.append(modified_line)
                else:
                    processed_lines.append(line)
            else:
                processed_lines.append(line)
        
        return '\n'.join(processed_lines)
    except Exception as e:
        return f"# Error adding data sampling: {str(e)}\n{code}"


def code_fix(code: str) -> str:
    """
    Apply comprehensive code fixes including library config management, 
    data sampling for plots, and font filtering.
    
    Args:
        code: Python code to process
        
    Returns:
        Fixed code with all improvements applied
    """
    try:
        # Apply library configuration management
        code, config_fixes = apply_library_config_management(code)
        # Add data sampling for better visualization readability
        code = add_data_sampling_for_plots(code)
        # Filter out matplotlib font-related code
        code = filter_plt_font_code(code)
        return code
    except Exception as e:
        return f"# Error in code fixes: {str(e)}\n{code}"


def apply_library_config_management(code: str) -> Tuple[str, List[str]]:
    """
    Apply library configuration management to fix environment-specific issues.
    
    This function detects and fixes problematic library configurations that may
    cause issues in different environments, such as:
    - matplotlib font settings
    - seaborn style configurations  
    - tensorflow/pytorch device settings
    - plotly renderer configurations
    - jupyter display settings
    
    Args:
        code: Python code to process
        
    Returns:
        Tuple of (processed_code, list_of_applied_fixes)
    """
    try:
        # Apply configuration fixes with medium severity threshold
        fixed_code, applied_fixes = apply_library_config_fixes(code, severity_threshold=1)
        return fixed_code, applied_fixes
    except Exception as e:
        return code, [f"Error in library config management: {str(e)}"]


def parse_last_python_code(raw_string: str) -> str:
    try:
        pat = re.compile(r"```python(?!.*```python)(.*?)```", re.DOTALL)
        python_code = re.findall(pat, raw_string)
        if python_code:
            code = python_code[0]
            return code
        return raw_string
    except Exception as e:
        return f"# Error parsing Python code: {str(e)}\n{raw_string}"


def extract_coding_assistant_content(text: str) -> str:
    try:
        pattern = r'<coding_assistant>(.*?)</coding_assistant>'
        matches = re.findall(pattern, text, flags=re.DOTALL)
        if matches:
            content = matches[0]
            return content
        return text
    except Exception as e:
        return f"# Error extracting coding assistant content: {str(e)}\n{text}"
    

def extract_required_packages_content(text: str) -> str:
    try:
        pattern = r'<required_packages>(.*?)</required_packages>'
        matches = re.findall(pattern, text, flags=re.DOTALL)
        if matches:
            return matches[0]
        return ""
    except Exception as e:
        return f"# Error extracting required packages content: {str(e)}\n{text}"


def parse_packages_from_code(code: str) -> list:
    """
    Parse all required packages from Python code more comprehensively.
    
    Handles various import patterns:
    - import pandas
    - import pandas as pd  
    - from sklearn import metrics
    - from sklearn.ensemble import RandomForestClassifier
    - import matplotlib.pyplot as plt
    - from package.subpackage import module
    """
    try:
        packages = set()
        lines = code.split('\n')
        
        # Known import name to package name mappings
        import_to_package = {
            'cv2': 'opencv-python',
            'PIL': 'Pillow', 
            'sklearn': 'scikit-learn',
            'bs4': 'beautifulsoup4',
            'Crypto': 'pycryptodome',
            'yaml': 'PyYAML',
            'skimage': 'scikit-image',
            'Bio': 'biopython',
            'pymc3': 'pymc3',
            'prophet': 'prophet',
            'fbprophet': 'prophet',  # deprecated, map to prophet
        }
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Pattern 1: import package
            import_match = re.match(r'import\s+([a-zA-Z_][a-zA-Z0-9_]*)', line)
            if import_match:
                module_name = import_match.group(1)
                # Get root package name (e.g., matplotlib from matplotlib.pyplot)
                root_package = module_name.split('.')[0]
                packages.add(import_to_package.get(root_package, root_package))
                continue
            
            # Pattern 2: import package as alias
            import_as_match = re.match(r'import\s+([a-zA-Z_][a-zA-Z0-9_\.]*)\s+as\s+\w+', line)
            if import_as_match:
                module_name = import_as_match.group(1)
                root_package = module_name.split('.')[0]
                packages.add(import_to_package.get(root_package, root_package))
                continue
            
            # Pattern 3: from package import ...
            from_match = re.match(r'from\s+([a-zA-Z_][a-zA-Z0-9_\.]*)\s+import', line)
            if from_match:
                module_name = from_match.group(1)
                root_package = module_name.split('.')[0]
                packages.add(import_to_package.get(root_package, root_package))
                continue
        
        # Remove common built-in modules that don't need installation
        builtin_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'random', 'math', 
            'collections', 'itertools', 'functools', 'operator', 'copy',
            'pickle', 're', 'string', 'threading', 'multiprocessing',
            'subprocess', 'pathlib', 'urllib', 'http', 'logging',
            'warnings', 'typing', 'abc', 'io', 'csv', 'sqlite3'
        }
        
        packages = packages - builtin_modules
        return sorted(list(packages))
        
    except Exception as e:
        return []


class NL2CodeParams(BaseModel):
    user_instruction: str = Field(..., description="Natural language instruction describing the desired code functionality")
    env_dependencies: Optional[List[str]] = Field(default_factory=lambda: ["pandas==1.5.0"])
    global_vars: Optional[Dict[str, str]] = Field(default_factory=dict)
    function_headers: Optional[List[str]] = Field(default_factory=list)
    previous_actions: Optional[List[Tuple[str, str, str]]] = Field(default_factory=list)
    data_type: Optional[str] = ""
    data_schema: Optional[str] = ""
    llm_name: Optional[str] = Field(default_factory=lambda: appConfig.common.llm.model_name)
    enable_code_fixes: Optional[bool] = Field(default=False, description="Enable comprehensive code fixes including library config management, data sampling, and font filtering")
    enable_action_manager: Optional[bool] = Field(default=False, description="Enable action manager to optimize previous_actions context by filtering redundant, failed, or irrelevant actions and improving relevance scoring")


class NL2CodeByScenarioParams(BaseModel):
    user_instruction: str = Field(..., description="Natural language instruction describing the desired code functionality")
    env_dependencies: Optional[List[str]] = Field(default_factory=lambda: ["pandas==1.5.0"])
    global_vars: Optional[Dict[str, str]] = Field(default_factory=dict)
    function_headers: Optional[List[str]] = Field(default_factory=list)
    previous_actions: Optional[List[Tuple[str, str, str]]] = Field(default_factory=list)
    data_type: Optional[str] = ""
    data_schema: Optional[str] = ""
    llm_name: Optional[str] = Field(default_factory=lambda: appConfig.common.llm.model_name)
    scenario: Optional[str] = Field(default="general", description="Data science scenario type (general, summary_stats, distribution_analysis, correlation_analysis, outlier_detection, data_preprocessing, feature_engineering, machine_learning)")
    auto_detect_scenario: Optional[bool] = Field(default=False, description="Automatically detect scenario from instruction if scenario is 'general'")
    enable_code_fixes: Optional[bool] = Field(default=False, description="Enable comprehensive code fixes including library config management, data sampling, and font filtering")
    enable_action_manager: Optional[bool] = Field(default=False, description="Enable action manager to optimize previous_actions context by filtering redundant, failed, or irrelevant actions and improving relevance scoring")


def optimize_previous_actions_with_manager(
    previous_actions: List[Tuple[str, str, str]], 
    user_instruction: str, 
    global_vars: Dict[str, str],
    scenario: str = "general"
) -> List[Tuple[str, str, str]]:
    """
    Optimize previous actions using the action manager.
    
    Args:
        previous_actions: List of (code, exec_status, output) tuples
        user_instruction: Current instruction for relevance scoring
        global_vars: Available global variables
        scenario: Data science scenario for context-aware optimization
        
    Returns:
        Optimized list of previous actions
    """
    if not previous_actions:
        return previous_actions
        
    try:
        # Create action manager with scenario-aware configuration
        config = ActionManagerConfig(
            max_actions=10,  # Reasonable limit for context
            max_tokens=3000,  # Control token usage
            filter_failed_actions=True,
            filter_redundant_actions=True,
            enable_summarization=True,
            min_relevance_threshold=2,  # Include LOW and above
            enable_relevance_scoring=True
        )
        
        manager = PreviousActionManager(config)
        
        # Process and optimize actions
        optimized_actions = manager.process_actions(
            raw_actions=previous_actions,
            current_instruction=user_instruction,
            context_vars=global_vars
        )
        
        return optimized_actions
        
    except Exception as e:
        # If action manager fails, return original actions
        print(f"Action manager optimization failed: {e}")
        return previous_actions


def context_builder(env_dependencies, global_vars, function_headers, previous_actions, data_type, data_schema):
    code_action_template = """Round {round}:
<coding_assistant>
Executed Code: 
```python
{code}
```
Execution Status: {exec_status} (Output: {output})
</coding_assistant>"""


    previous_actions = [code_action_template.format(
        round=round,
        code=code,
        exec_status=exec_status,
        output=output,
    ) for round, (code, exec_status, output) in enumerate(previous_actions)]

    previous_actions = "\n".join(previous_actions)

    context = ""
    if env_dependencies:
        context += f"Installed Packages: {env_dependencies}\n"
    if global_vars:
        global_vars = [f"{k} = {v}" for k, v in global_vars.items()]
        global_vars = "\n".join(global_vars)
        context += f"Global Variables: {global_vars}\n"
    if function_headers:
        context += f"Relative Function Headers: {function_headers}\n"
    if data_type and data_schema:
        context += f"USER's {data_type} Schema: {data_schema}\n"
    if previous_actions:
        context += f"Previous Generated Code and Execution Results: {previous_actions}\n"

    return context


def nl2code(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate executable Python code based on user instruction and programming context.
    
    This function orchestrates the code generation process by building context from various parameters,
    then leveraging an LLM to generate appropriate Python code according to the given instruction.

    Args:
        user_instruction (str): Natural language instruction describing the desired code functionality
        env_dependencies (list): List of installed packages/libraries (e.g., ['pandas', 'numpy'])
        global_vars (dict): Dictionary of existing global variables (e.g., {'df': "pd.DataFrame()"})
        function_headers (list): List of available function signatures/headers (e.g., ['def process_data(data):...'])
        previous_actions (list): List of tuples representing previous code attempts and their execution results. Each tuple format: (code_str, exec_status_str, output_str)
        data_type (str): Type of user's primary data object (e.g., 'DataFrame', 'Array')
        data_schema (str): Schema description of user's primary data (e.g., 'columns: [id, name]')
        model_name (str): Name of the LLM model to use for code generation (e.g., 'DeepSeek-V3-0324')

    Returns:
        str: Generated Python code ready for execution. The code will:
            - Use existing global variables and functions
            - Follow Python data processing conventions (Pandas/Spark for data, Matplotlib for visualization)
            - Avoid non-executable elements like pseudocode or placeholders

    The implementation combines contextual information (environment setup, variables, previous attempts) with
    the user's instruction to produce executable code. It handles error recovery patterns and maintains
    compatibility with Jupyter Notebook environments.
    """
    try:
        validated_params = NL2CodeParams(**params)
    except ValidationError as ve:
        return {
            "error": "Parameter validation failed",
            "details": ve.errors()
        }

    try:
        # Optimize previous actions with action manager if enabled
        previous_actions = validated_params.previous_actions
        if validated_params.enable_action_manager and previous_actions:
            previous_actions = optimize_previous_actions_with_manager(
                previous_actions=previous_actions,
                user_instruction=validated_params.user_instruction,
                global_vars=validated_params.global_vars,
                scenario="general"  # Use general scenario for non-scenario functions
            )
        
        context = context_builder(
            env_dependencies=validated_params.env_dependencies,
            global_vars=validated_params.global_vars,
            function_headers=validated_params.function_headers,
            previous_actions=previous_actions,
            data_type=validated_params.data_type,
            data_schema=validated_params.data_schema
        )
        response = code_generator(context, validated_params.user_instruction, validated_params.llm_name)
        
        python_code = parse_last_python_code(response)
        python_code = extract_coding_assistant_content(python_code)
        
        # Apply code fixes if enabled
        if validated_params.enable_code_fixes:
            python_code = code_fix(python_code)
        
        return {
            "python_code": python_code
        }
    except Exception as e:
        return {
            "error": "Code generation failed.",
            "details": str(e)
        }
    

def nl2code_w_pkgs(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate executable Python code based on user instruction and programming context.
    
    This function orchestrates the code generation process by:
    1. Validating input parameters
    2. Building execution context from dependencies, variables, and history
    3. Generating Python code using an LLM
    4. Extracting executable code and required packages

    Args:
        params: Dictionary containing these required keys:
            user_instruction (str): Natural language instruction describing desired functionality
            env_dependencies (list): Installed packages/libraries (e.g., ['pandas', 'numpy'])
            global_vars (dict): Existing global variables (e.g., {'df': "pd.DataFrame()"})
            function_headers (list): Available function signatures (e.g., ['def process_data(data):...'])
            previous_actions (list): Tuples of past code attempts and results: Format: [(code_str, exec_status_str, output_str), ...]
            data_type (str): Type of primary data object (e.g., 'DataFrame', 'Array')
            data_schema (str): Schema description of primary data (e.g., 'columns: [id, name]')
            model_name (str): LLM model for code generation (e.g., 'DeepSeek-V3-0324')

    Returns:
        Dictionary with these possible structures:
        
        Success case:
            {
                "python_code": str,  # Executable Python code
                "required_packages": list[str]  # Packages needed for execution
            }
        
        Error cases:
            {
                "error": "Parameter validation failed",
                "details": list[dict]  # Pydantic validation errors
            }
            OR
            {
                "error": "Code generation failed.",
                "details": str  # Exception message
            }

    The generated code:
        - Uses existing global variables and functions
        - Follows Python data processing conventions
        - Avoids non-executable elements
        - Includes error recovery patterns
        - Maintains Jupyter Notebook compatibility
    """
    try:
        validated_params = NL2CodeParams(**params)
    except ValidationError as ve:
        return {
            "error": "Parameter validation failed",
            "details": ve.errors()
        }

    try:
        # Optimize previous actions with action manager if enabled
        previous_actions = validated_params.previous_actions
        if validated_params.enable_action_manager and previous_actions:
            previous_actions = optimize_previous_actions_with_manager(
                previous_actions=previous_actions,
                user_instruction=validated_params.user_instruction,
                global_vars=validated_params.global_vars,
                scenario="general"  # Use general scenario for non-scenario functions
            )
        
        context = context_builder(
            env_dependencies=validated_params.env_dependencies,
            global_vars=validated_params.global_vars,
            function_headers=validated_params.function_headers,
            previous_actions=previous_actions,
            data_type=validated_params.data_type,
            data_schema=validated_params.data_schema
        )
        response = code_generator(context, validated_params.user_instruction, validated_params.llm_name)
        
        python_code = response
        python_code = parse_last_python_code(python_code)
        python_code = extract_coding_assistant_content(python_code)
        # Apply code fixes if enabled
        if validated_params.enable_code_fixes:
            python_code = code_fix(python_code)
    except Exception as e:
        return {
            "error": "Code generation failed.",
            "details": str(e)
        }
    
    required_packages = response
    try:
        required_packages = extract_required_packages_content(required_packages)
        python_code_required_packages = parse_last_python_code(required_packages)
        if python_code_required_packages != required_packages:
            required_packages = re.findall(r"\s*import\s+(\w+)", python_code_required_packages)
            required_packages += re.findall(r"\s*from\s+(\w+)", python_code_required_packages)
            required_packages = list(set(required_packages))
        else:
            required_packages = [i.strip() for i in re.split(',|\n', required_packages) if i.strip()]
    except Exception as e:
        required_packages = []
    return {
        "python_code": python_code,
        "required_packages": required_packages
    }


def code_generator(context, user_instruction, model_name):
    system_prompt = f"""
You are a powerful agentic AI coding assistant, powered by {model_name}.

You are pair programming with a USER to solve their coding task, generate the required package names denoted by the <required_packages> tag and the final python code denoted by the <coding_assistant> tag.
Each time the USER sends a message, we may automatically attach some information about their current state, such as installed packages, global variables, function headers, user data schema, previous generated code and execution results, and more.
Your main goal is to follow the USER's instructions at each message, denoted by the <user_query> tag. 

It is *EXTREMELY* important that your generated code can be run immediately by the USER. To ensure this, follow these instructions carefully:
1. You generate code to run in the Jupyter Notebook. You can directly use global vars and relative functions in your generated code.
2. Generate code for data fetching using PySpark, data processing using Pandas, and data visualization using Matplotlib.
3. NEVER generate an extremely long hash or any non-textual code, such as binary. These are not helpful to the USER and are very expensive.
4. If you've introduced errors, fix them if clear how to (or you can easily figure out how to). Do not make uneducated guesses. And DO NOT loop more than 3 times on fixing errors. On the third time, you should stop and ask the user what to do next.
5. Extract all third-party dependencies from the Python code. Parse all import patterns (import X, from X import Y, from X.Y.Z import *, etc.) and output the correct pip package names, one per line, sorted alphabetically. For nested imports (e.g., matplotlib.pyplot), extract only the root package name (matplotlib).
6. If you use prophet, do not use fbprophet, it is deprecated. Use prophet instead, for example "from prophet import Prophet".

Please notice these are examples: import names may not same as the package names.
import_name    package_name
prophet        prophet
sklearn        scikit-learn  
PIL            Pillow  
cv2            opencv-python  
bs4            beautifulsoup4  
Crypto         pycryptodome  
yaml           PyYAML  
skimage        scikit-image  
Bio            biopython  
pymc3          pymc3  

Answer the user's instructions generating correct code. IF there are missing values for required parameters, ask the user to supply these values; otherwise proceed with the code generation. If the user provides a specific value for a parameter (for example provided in quotes), make sure to use that value EXACTLY. DO NOT make up values for or ask about optional parameters. Carefully analyze descriptive terms in the request as they may indicate required parameter values that should be included even if not explicitly quoted.
"""
# 2. Generate code for data fetching using PySpark, data processing using Pandas, and data visualization using Matplotlib.

    user_prompt = f"""
<code_gen_context>
{context}
</code_gen_context>

<user_query>
USER's instructions: {user_instruction}
</user_query>
"""

    messages = [{
        "role": "system",
        "content": system_prompt,
    },{
        "role": "user",
        "content": user_prompt
    }]
    response = llm_for_openai(messages, model=model_name)
    return response


def code_generator_by_scenario(context, user_instruction, model_name, scenario_enhancement=""):
    # Base system prompt
    base_system_prompt = f"""
You are a powerful agentic AI coding assistant, powered by {model_name}.

You are pair programming with a USER to solve their coding task, generate the required package names denoted by the <required_packages> tag and the final python code denoted by the <coding_assistant> tag.
Each time the USER sends a message, we may automatically attach some information about their current state, such as installed packages, global variables, function headers, user data schema, previous generated code and execution results, and more.
Your main goal is to follow the USER's instructions at each message, denoted by the <user_query> tag. 

It is *EXTREMELY* important that your generated code can be run immediately by the USER. To ensure this, follow these instructions carefully:
1. You generate code to run in the Jupyter Notebook. You can directly use global vars and relative functions in your generated code.
2. Generate code for data fetching using PySpark, data processing using Pandas, and data visualization using Matplotlib.
3. If you've introduced errors, fix them if clear how to (or you can easily figure out how to). Do not make uneducated guesses. And DO NOT loop more than 3 times on fixing errors. On the third time, you should stop and ask the user what to do next.
4. For data visualization, automatically handle large datasets by sampling data for better readability. When plotting data with more than 15 rows, use techniques like df.head(15) to limit the display to the first 15 rows. Add comments explaining the sampling strategy.
5. Extract all third-party dependencies from the Python code. Parse all import patterns (import X, from X import Y, from X.Y.Z import *, etc.) and output the correct pip package names, one per line, sorted alphabetically. For nested imports (e.g., matplotlib.pyplot), extract only the root package name (matplotlib).
6. If you use prophet, do not use fbprophet, it is deprecated. Use prophet instead, for example: use "from prophet import Prophet".
7. If you use ploty Figure to show result, use renderer="png".

It is *EXTREMELY* important NOT to do the following things:
1. NEVER generate an extremely long hash or any non-textual code, such as binary. These are not helpful to the USER and are very expensive.
2. NEVER generate matplotlib font-related code such as plt.rcParams['font.family'], matplotlib.rcParams['font.sans-serif'], fontsize, fontfamily, fontname, fontweight, fontstyle parameters, or any other font configuration. This prevents font warnings when the specified fonts are not available.
3. AVOID environment-specific library configurations that may not work in all environments, including:
   - DO NOT USE matplotlib backend settings (matplotlib.use(), plt.switch_backend())
   - DO NOT USE seaborn global style settings (sns.set_style(), sns.set_palette())  
   - DO NOT USE tensorflow/pytorch device-specific configurations (tf.device('/gpu:0'), .cuda())
   - DO NOT USE plotly renderer configurations (pio.renderers.default)
   - DO NOT USE jupyter-specific display settings (%matplotlib, get_ipython())
   - DO NOT USE opencv backend configurations
   - DO NOT USE pandas global display options that may cause compatibility issues
   - DO NOT CREATE spark session, spark session is available in environment, use "spark" session directly.
   Use conditional checks or try-except blocks when device/backend selection is necessary.
   
Please notice these are examples: import names may not same as the package names.
import_name    package_name
prophet        prophet
sklearn        scikit-learn  
PIL            Pillow  
cv2            opencv-python  
bs4            beautifulsoup4  
Crypto         pycryptodome  
yaml           PyYAML  
skimage        scikit-image  
Bio            biopython  
pymc3          pymc3  

Answer the user's instructions generating correct code. IF there are missing values for required parameters, ask the user to supply these values; otherwise proceed with the code generation. If the user provides a specific value for a parameter (for example provided in quotes), make sure to use that value EXACTLY. DO NOT make up values for or ask about optional parameters. Carefully analyze descriptive terms in the request as they may indicate required parameter values that should be included even if not explicitly quoted.
"""

    # Add scenario-specific enhancement if provided
    if scenario_enhancement:
        system_prompt = base_system_prompt + "\n\n" + scenario_enhancement
    else:
        system_prompt = base_system_prompt

    user_prompt = f"""
<code_gen_context>
{context}
</code_gen_context>

<user_query>
USER's instructions: {user_instruction}
</user_query>
"""

    messages = [{
        "role": "system",
        "content": system_prompt,
    },{
        "role": "user",
        "content": user_prompt
    }]
    response = llm_for_openai(messages, model=model_name)
    return response


def code_verifier():
    pass


def nl2code_by_scenario(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate executable Python code based on user instruction and programming context.
    
    This function orchestrates the code generation process by building context from various parameters,
    then leveraging an LLM to generate appropriate Python code according to the given instruction.

    Args:
        user_instruction (str): Natural language instruction describing the desired code functionality
        env_dependencies (list): List of installed packages/libraries (e.g., ['pandas', 'numpy'])
        global_vars (dict): Dictionary of existing global variables (e.g., {'df': "pd.DataFrame()"})
        function_headers (list): List of available function signatures/headers (e.g., ['def process_data(data):...'])
        previous_actions (list): List of tuples representing previous code attempts and their execution results. Each tuple format: (code_str, exec_status_str, output_str)
        data_type (str): Type of user's primary data object (e.g., 'DataFrame', 'Array')
        data_schema (str): Schema description of user's primary data (e.g., 'columns: [id, name]')
        model_name (str): Name of the LLM model to use for code generation (e.g., 'DeepSeek-V3-0324')

    Returns:
        str: Generated Python code ready for execution. The code will:
            - Use existing global variables and functions
            - Follow Python data processing conventions (Pandas/Spark for data, Matplotlib for visualization)
            - Avoid non-executable elements like pseudocode or placeholders

    The implementation combines contextual information (environment setup, variables, previous attempts) with
    the user's instruction to produce executable code. It handles error recovery patterns and maintains
    compatibility with Jupyter Notebook environments.
    """
    try:
        validated_params = NL2CodeByScenarioParams(**params)
    except ValidationError as ve:
        return {
            "error": "Parameter validation failed",
            "details": ve.errors()
        }

    try:
        # Determine the scenario
        scenario_name = validated_params.scenario
        if validated_params.auto_detect_scenario and scenario_name == "general":
            scenario_name = detect_scenario(validated_params.user_instruction)
        
        # Optimize previous actions with action manager if enabled
        previous_actions = validated_params.previous_actions
        if validated_params.enable_action_manager and previous_actions:
            previous_actions = optimize_previous_actions_with_manager(
                previous_actions=previous_actions,
                user_instruction=validated_params.user_instruction,
                global_vars=validated_params.global_vars,
                scenario=scenario_name  # Use detected scenario for context-aware optimization
            )
        
        # Get scenario-specific enhancements
        scenario_obj = scenario_manager.get_scenario_by_name(scenario_name)
        scenario_enhancement = ""
        if scenario_obj and scenario_name != "general":
            scenario_enhancement = scenario_manager.get_scenario_prompt_enhancement(scenario_obj)
        
        context = context_builder(
            env_dependencies=validated_params.env_dependencies,
            global_vars=validated_params.global_vars,
            function_headers=validated_params.function_headers,
            previous_actions=previous_actions,
            data_type=validated_params.data_type,
            data_schema=validated_params.data_schema
        )
        response = code_generator_by_scenario(context, validated_params.user_instruction, validated_params.llm_name, scenario_enhancement)
        
        python_code = response
        python_code = parse_last_python_code(python_code)
        python_code = extract_coding_assistant_content(python_code)
        
        # Apply code fixes if enabled
        if validated_params.enable_code_fixes:
            python_code = code_fix(python_code)
        
        return {
            "python_code": python_code,
            "detected_scenario": scenario_name
        }
    except Exception as e:
        return {
            "error": "Code generation failed.",
            "details": str(e)
        }
    

def nl2code_w_pkgs_by_scenario(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate executable Python code based on user instruction and programming context.
    
    This function orchestrates the code generation process by:
    1. Validating input parameters
    2. Building execution context from dependencies, variables, and history
    3. Generating Python code using an LLM
    4. Extracting executable code and required packages

    Args:
        params: Dictionary containing these required keys:
            scenario (str): Data science scenario type (general, summary_stats, distribution_analysis, correlation_analysis, outlier_detection, data_preprocessing, feature_engineering, machine_learning)
            user_instruction (str): Natural language instruction describing desired functionality
            env_dependencies (list): Installed packages/libraries (e.g., ['pandas', 'numpy'])
            global_vars (dict): Existing global variables (e.g., {'df': "pd.DataFrame()"})
            function_headers (list): Available function signatures (e.g., ['def process_data(data):...'])
            previous_actions (list): Tuples of past code attempts and results: Format: [(code_str, exec_status_str, output_str), ...]
            data_type (str): Type of primary data object (e.g., 'DataFrame', 'Array')
            data_schema (str): Schema description of primary data (e.g., 'columns: [id, name]')
            model_name (str): LLM model for code generation (e.g., 'DeepSeek-V3-0324')

    Returns:
        Dictionary with these possible structures:
        
        Success case:
            {
                "python_code": str  # Executable Python code
                "required_packages": list[str]  # Packages needed for execution
                "detected_scenario": str # scenario_name,
            }
        
        Error cases:
            {
                "error": "Parameter validation failed",
                "details": list[dict]  # Pydantic validation errors
            }
            OR
            {
                "error": "Code generation failed.",
                "details": str  # Exception message
            }

    The generated code:
        - Uses existing global variables and functions
        - Follows Python data processing conventions
        - Avoids non-executable elements
        - Includes error recovery patterns
        - Maintains Jupyter Notebook compatibility
    """
    try:
        validated_params = NL2CodeByScenarioParams(**params)
    except ValidationError as ve:
        return {
            "error": "Parameter validation failed",
            "details": ve.errors()
        }

    try:
        # Determine the scenario
        scenario_name = validated_params.scenario
        if validated_params.auto_detect_scenario and scenario_name == "general":
            scenario_name = detect_scenario(validated_params.user_instruction)
        
        # Optimize previous actions with action manager if enabled
        previous_actions = validated_params.previous_actions
        if validated_params.enable_action_manager and previous_actions:
            previous_actions = optimize_previous_actions_with_manager(
                previous_actions=previous_actions,
                user_instruction=validated_params.user_instruction,
                global_vars=validated_params.global_vars,
                scenario=scenario_name  # Use detected scenario for context-aware optimization
            )
        
        # Get scenario-specific enhancements
        scenario_obj = scenario_manager.get_scenario_by_name(scenario_name)
        scenario_enhancement = ""
        if scenario_obj:
            scenario_enhancement = scenario_manager.get_scenario_prompt_enhancement(scenario_obj)
        
        context = context_builder(
            env_dependencies=validated_params.env_dependencies,
            global_vars=validated_params.global_vars,
            function_headers=validated_params.function_headers,
            previous_actions=previous_actions,
            data_type=validated_params.data_type,
            data_schema=validated_params.data_schema
        )
        response = code_generator_by_scenario(context, validated_params.user_instruction, validated_params.llm_name, scenario_enhancement)
        
        python_code = response
        python_code = parse_last_python_code(python_code)
        python_code = extract_coding_assistant_content(python_code)
        
        # Apply code fixes if enabled
        if validated_params.enable_code_fixes:
            python_code = code_fix(python_code)

    except Exception as e:
        return {
            "error": "Code generation failed.",
            "details": str(e)
        }
    
    # Parse required packages from the generated code
    required_packages = []
    try:
        # First try to extract packages from <required_packages> tags if present
        packages_from_tags = extract_required_packages_content(response)
        if packages_from_tags:
            # If we have tagged packages, parse them
            packages_code = parse_last_python_code(packages_from_tags)
            if packages_code != packages_from_tags:
                # If it's actual code with imports
                required_packages = parse_packages_from_code(packages_code)
            else:
                # If it's a simple list separated by comma or newline
                required_packages = [i.strip() for i in re.split(',|\n', packages_from_tags) if i.strip()]
        
        # Also parse packages directly from the generated Python code
        code_packages = parse_packages_from_code(python_code)
        
        # Combine and deduplicate packages from both sources
        all_packages = set(required_packages + code_packages)
        required_packages = sorted(list(all_packages))
        
    except Exception as e:
        # Fallback: parse directly from the generated code
        required_packages = parse_packages_from_code(python_code)
    return {
        "python_code": python_code,
        "required_packages": required_packages,
        "detected_scenario": scenario_name
    }


if __name__ == "__main__":
    params = {
        "user_instruction": "Plot the sales data over time using matplotlib",
        "global_vars": {"df": "your_dataframe"},
        "data_type": "DataFrame",
        "data_schema": "columns: [date, sales]",
        "model_name": "DeepSeek-R1"
    }

    result = nl2code(params)

    if "python_code" in result:
        print(result["python_code"])
    else:
        print("Error:", result.get("error"), result.get("details"))
