import json
from datetime import datetime, timezone
from common.logger.logger import logger, get_trace_logger
from infra.adapter.user_session_adapter import UserSessionAdapter
from infra.domain.user_session_entity import UserSession
from infra.jupyter.manager import get_global_manager
from infra.memory.chat_es_operator import Chat<PERSON><PERSON>perator, SessionData
from infra.memory.jupyter_operator import Ju<PERSON><PERSON><PERSON><PERSON>, Jupyter<PERSON>perator
from infra.memory.memory import factory


class ChatAsyncTaskManager:
    def __init__(self, ctx):
        self.ctx = ctx
        self.logger = get_trace_logger(ctx, "chat")

    async def store_memory(self, params, complete_response, complete_think, task_list, final_summary, error_context):
        ctx = self.ctx
        try:
            self.logger.info(f"开始保存会话记忆")
            sample_messages = [
                {"role": "user", "content": params.question},
                {"role": "assistant", "content": complete_response},
            ]
            mm = await factory.get_memory_manager(ctx.app_id)
            await mm.add_memory(
                messages=sample_messages,
                sub_account_uin=ctx.sub_account_uin,
                agent_id=params.agent_id,
                run_id=ctx.session_id,
            )
        except Exception as store_memory_e:
            self.logger.error(f"保存mem0会话记忆失败: {store_memory_e},trace_id: {ctx.trace_id}")
        try:
            chat_es_operator = ChatESOperator.get_instance(ctx.app_id)
            session_data = SessionData(
                agent_id=params.agent_id,
                sub_account_uin=ctx.sub_account_uin,
                record_id=params.record_id,
                session_id=ctx.session_id,
                question=params.question,
                thinking_chain=complete_think,
                task_list=task_list,
                context=params.req_context,
                final_summary=final_summary,
                db_info=params.db_info,
                answer=complete_response,
                error_context=error_context,
                create_time=datetime.now(timezone.utc),
                update_time=datetime.now(timezone.utc))
            self.logger.info(f"保存会话记忆 - session_data: {session_data}")
            await chat_es_operator.async_save_chat_record(session_data)
            self.logger.info(f"成功保存会话记忆成功")
        except Exception as store_memory_e:
            self.logger.error(f"保存会话记忆失败: {store_memory_e},trace_id: {ctx.trace_id}")

    async def store_jupyter_memory(self, params, studio_jupyter):
        ctx = self.ctx
        try:
            self.logger.info(f"开始保存store_jupyter_memory开始")
            jupyters = []
            for jupyter_id_key, jupyter_item in studio_jupyter.items():
                self.logger.info(f"jupyter_id_key is : {jupyter_id_key}")
                jupter = jupyter_item.get("v", {})
                jupyters.append(JupyterData(
                    sub_account_uin=ctx.sub_account_uin,
                    session_id=ctx.session_id,
                    record_id=params.record_id,
                    cell_id=jupter.get('id'),
                    jupyter=jupter
                ))
            jupyter_operator = JupyterOperator.get_instance(ctx.app_id)
            await jupyter_operator.async_save_jupyter_records(jupyter_records=jupyters)
            self.logger.info(f"成功保存store_jupyter_memory, trace_id: {ctx.trace_id}")
        except Exception as store_memory_e:
            self.logger.error(f"保存store_jupyter_memory error: {store_memory_e},trace_id: {ctx.trace_id}")

    async def update_session_time(self, params, mysql_pool):
        ctx = self.ctx
        self.logger.info(f"更新会话信息 - SubAccountUin: {ctx.sub_account_uin}, SessionID: {ctx.session_id}")
        try:
            session_adapter = UserSessionAdapter(mysql_pool)
            db_info = params.db_info
            success = session_adapter.update_session_info(params.ctx, db_info)
            if not success:
                logger.error(f"更新会话信息失败 - SubAccountUin: {ctx.sub_account_uin}, SessionID: {ctx.session_id}")
            self.logger.info(f"更新会话信息成功 - SubAccountUin: {ctx.sub_account_uin}, SessionID: {ctx.session_id}")
        except Exception as update_session_error:
            self.logger.error(f"更新会话信息异常: {update_session_error}")

    @staticmethod
    async def stop_kernel(ctx):
        kernel_manager = get_global_manager()
        if kernel_manager is not None:
            await kernel_manager.session_stop(ctx.trace_id)

    def add_tasks(self, background_tasks, params, complete_response, complete_think,
                  task_list, final_summary, studio_jupyter, error_context, mysql_pool):
        background_tasks.add_task(self.stop_kernel, self.ctx)
        background_tasks.add_task(self.store_memory, params, complete_response, complete_think,
                                  task_list, final_summary, error_context)
        if studio_jupyter:
            background_tasks.add_task(self.store_jupyter_memory, params, studio_jupyter)
        background_tasks.add_task(self.update_session_time, params, mysql_pool)
