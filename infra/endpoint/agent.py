import asyncio
import uuid
import copy
import os
import ray
import json
import time

from common.elements.agent_event import ErrorEvent
from common.logger.logger import logger, get_trace_logger
from common.share.context import Context, ChatContext
from common.share.stream_param import StreamGenerationParams
from common.tencentcloud.cos import upload_byte_stream
from infra.datascience_agent.agent_service import AgentService
from infra.jupyter.manager import KernelManager
from infra.mcp.manager.mcp_manager import MC<PERSON>anager
from infra.guardrail.guardrail import Guardrail
from common.share.error import ErrorCode
from collections import deque
from abc import ABC, abstractmethod
from common.share.config import appConfig
from common.elements.agent_event import AgentEvent
from common.share import env
from opentelemetry.trace import use_span
EXECUTION_MODE = os.getenv("EXECUTION_MODE", "ray").lower()

class BaseAgent(ABC):
    """Agent抽象基类，定义所有Agent必须实现的接口"""

    @abstractmethod
    async def chat(self, params: StreamGenerationParams):
        pass

    @abstractmethod
    async def get_state(self):
        pass

    @abstractmethod
    async def stop(self):
        pass

    @classmethod
    @abstractmethod
    async def get_or_create_agent(cls, ctx: Context, model: str, params: StreamGenerationParams, **kwargs):
        pass


@ray.remote(scheduling_strategy="SPREAD")
class RayAgent:
    """Ray分布式执行模式的Agent实现"""

    def __init__(self, ctx: Context, model: str = "", params: StreamGenerationParams = None, **kwargs):
        os.chdir(os.getenv("WORKING_DIR", "/tmp/ray/"))
        self.model = model
        self.extra = kwargs
        self.state = "ready"
        self.ctx = ctx
        self.logger = get_trace_logger(ctx, "chat")
        self.agent_runner = AgentRunner(ctx, model, params, **kwargs)
        self.last_active = time.time()
        self.timeout = appConfig.common.time_out.actor_time_out  # 30分钟
        self.actor_name = f"{ctx.sub_account_uin}_{ctx.session_id}"
        asyncio.get_event_loop().create_task(self._self_cleanup())
        self.queue = None
        self.resume_subscribers = {}

    async def _self_cleanup(self):
        self.logger.info(f"[RayAgent][{self.actor_name}] start !")
        while True:
            await asyncio.sleep(60)
            self.logger.info(f"[RayAgent][{self.actor_name}] 检查自杀: last_active={self.last_active}, timeout={self.timeout}")
            if time.time() - self.last_active > self.timeout:
                self.logger.warning(f"[RayAgent][{self.actor_name}] 超时自杀: last_active={self.last_active}, now={time.time()}, timeout={self.timeout}")
                try:
                    ray.actor.exit_actor()
                except Exception as e:
                    self.logger.info(f"Exception during exit_actor: {e}", exc_info=True)
                    # 兜底，强制退出
                os._exit(0)
                self.logger.info(f"[RayAgent][{self.actor_name}] 自杀完成")
                break

    def update_activity(self):
        self.logger.info(f"[RayAgent][{self.actor_name}] 更新活跃时间")
        self.last_active = time.time()

    async def chat(self, params: StreamGenerationParams):
        """启动流式聊天，将chunk放入Ray队列"""
        self.logger.info(f"RayAgent chat start, params: {params}")
        self.queue = asyncio.Queue()
        self.ctx = params.ctx
        self.logger.info(f"RayAgent chat start, ctx: {self.ctx}")
        self.update_activity()
        try:
            self.logger.info(f"RayAgent chat start, ctx: {self.ctx}")
            async for chunk in self.agent_runner.chat(params):
                await self.queue.put(chunk)
            await self.queue.put("__STREAM_END__")
            self.logger.info(f"RayAgent chat end, ctx: {self.ctx}")
        except Exception as e:
            self.logger.error(f"RayAgent chat error: {e}", exc_info=True)
            await self.queue.put(ErrorEvent(err_code=ErrorCode.InternalError))
            await self.queue.put("__STREAM_END__")
        # finally:
        #     self.queue = None

    async def get_next_chunk(self, timeout=1.0):
        try:
            chunk = await asyncio.wait_for(self.queue.get(), timeout=timeout)
            return chunk
        except asyncio.TimeoutError:
            return None

    async def is_ok(self):
        return True

    async def get_state(self):
        return self.state

    async def stop(self):
        """停止当前Agent"""
        self.logger.info(f"RayAgent stop start, session_id: {self.ctx.session_id}, trace_id: {self.ctx.trace_id}")
        self.state = "stopped"
        await self.agent_runner.stop()
        await self.agent_runner.wait_stop()
        self.logger.info(f"RayAgent stop end, session_id: {self.ctx.session_id}, trace_id: {self.ctx.trace_id}")


    async def start_resume_stream(self, record_id):
        """
        为每个resume请求创建独立订阅，返回subscriber_id
        """
        subscriber_id = str(uuid.uuid4())
        broadcaster = self.agent_runner.get_broadcaster(record_id)
        # 这里直接用 subscribe()，它会replay历史并持续推送新chunk
        self.resume_subscribers[(record_id, subscriber_id)] = broadcaster.subscribe()
        return subscriber_id

    async def get_next_resume_chunk(self, record_id, subscriber_id):
        key = (record_id, subscriber_id)
        if key not in self.resume_subscribers:
            return None
        try:
            # 获取该订阅的 async generator
            agen = self.resume_subscribers[key]
            chunk = await agen.__anext__()
            if chunk == "__END__":
                # 结束后清理
                del self.resume_subscribers[key]
            return chunk
        except StopAsyncIteration:
            # 订阅结束
            del self.resume_subscribers[key]
            return None


class LocalAgent(BaseAgent):
    """本地执行模式的Agent实现"""

    _instances = {}  # {actor_name: instance}
    _last_active_times = {}  # {actor_name: last_active_timestamp}

    def __init__(self, ctx: Context, model: str = "", **kwargs):
        self.ctx = ctx
        self.model = model
        self.extra = kwargs
        self.state = "ready"
        actor_name = f"{ctx.sub_account_uin}_{ctx.session_id}"
        self.agent_runner = None
        self.session_id = ctx.session_id
        self.trace_id = ctx.trace_id
        self.__class__._instances[actor_name] = self
        self.__class__._last_active_times[actor_name] = time.time()  # 初始化活跃时间
        self.logger = get_trace_logger(ctx, "chat")

    @classmethod
    def update_activity(cls, actor_name):
        cls._last_active_times[actor_name] = time.time()

    async def chat(self, params: StreamGenerationParams):
        self.ctx = params.ctx
        try:
            self.logger.info(f"LocalAgent chat start, session_id: {self.session_id}, trace_id: {self.trace_id}, message: {params.question}")
            async for chunk in self.agent_runner.chat(params):
                yield chunk
            self.logger.info(f"LocalAgent chat end, session_id: {self.session_id}, trace_id: {self.trace_id}, message: {params.question}")
        except Exception as e:
            self.logger.error(f"LocalAgent chat error: {e} session_id: {self.session_id}, trace_id: {self.trace_id}, message: {params.question}", exc_info=True)
            yield ErrorEvent(err_code=ErrorCode.InternalError)
            return

    async def get_state(self):
        return self.state

    async def stop(self):
        """停止当前Agent"""
        self.state = "stopped"

    @classmethod
    async def get_or_create_agent(cls, ctx: Context, model, params: StreamGenerationParams, **kwargs):
        actor_name = f"{ctx.sub_account_uin}_{ctx.session_id}"
        if actor_name in cls._instances:
            logger.info(f"获取现有本地Agent: {actor_name}")
            cls.update_activity(actor_name)
            return cls._instances[actor_name]

        logger.info(f"创建新本地Agent: {actor_name}")
        instance = cls(ctx=ctx, model=model, **kwargs)
        instance.agent_runner = AgentRunner(ctx, model, params, **kwargs)
        cls._instances[actor_name] = instance
        cls._last_active_times[actor_name] = time.time()
        return instance

    async def agent_stop(self):
        """停止指定的本地Agent实例"""
        try:
            await self.agent_runner.stop()
            await self.agent_runner.wait_stop()

            self.logger.info(f"LocalAgent agent_stop success")
            return True
        except Exception as e:
            self.logger.error(f"LocalAgent stop error: {e}", exc_info=True)
            return False

    def get_local_agent_runner(self):
        actor_name = f"{self.ctx.sub_account_uin}_{self.session_id}"
        agent = LocalAgent._instances.get(actor_name)
        if agent:
            return agent.agent_runner
        return None


class NotebookSaver:
    def __init__(self, params: StreamGenerationParams):
        self.path = f"notebooks/{params.ctx.get_session_id()}/{params.ctx.get_trace_id()}.ipynb"
        self.contents = []
        self.thinking = []
        self.messages = []
        self.in_message = False
        self.is_saved = False
        self.valid = env.DUMP_TO_NOTEBOOK
        logger.info(f"NotebookSaver init, path: {self.path}, valid: {self.valid}")

    async def add_notebook_content(self, cell):
        if not self.valid or cell is None:
            return
        if isinstance(cell, str):
            self.contents.append(
                {"cell_type": "markdown", "execution_count": 0, "source": [cell], "outputs": [], "metadata": {}}
            )
        elif isinstance(cell, dict) and "cell_type" in cell:
            self.contents.append(cell)
        elif isinstance(cell, dict) or isinstance(cell, list):
            self.contents.append(
                {"cell_type": "markdown", "execution_count": 0, "source": [json.dumps(cell)], "outputs": [], "metadata": {}}
            )
        await self.save()

    async def chunk_to_notebook(self, chunk: AgentEvent):
        if chunk.event_type == "text":
            await self.add_notebook_content("".join(self.thinking))
            return
        if chunk.event_type == "think":
            self.thinking.append(chunk.content)
            return
        elif chunk.event_type == "message":
            self.messages.append(chunk.content)
            self.in_message = True
            return
        if self.in_message:
            self.in_message = False
            await self.add_notebook_content("".join(self.messages))
        if chunk.event_type == "studio_jupyter":
            await self.add_notebook_content(chunk.to_dict()["v"])
        else:
            await self.add_notebook_content(chunk.content)

    async def save(self):
        if self.valid:
            os.makedirs(os.path.dirname(self.path), exist_ok=True)
            with open(self.path, "w", encoding="utf-8") as f:
                json.dump({"cells": self.contents}, f, ensure_ascii=False, indent=2)

    async def close(self):
        logger.info(f"NotebookSaver close, path: {self.path}, valid: {self.valid}, is_saved: {self.is_saved}")
        if not self.is_saved and self.valid:
            if self.in_message:
                await self.add_notebook_content("".join(self.messages))
                self.in_message = False
            logger.info(f"NotebookSaver save, path: {self.path}, valid: {self.valid}, is_saved: {self.is_saved}")
            await self.save()
            self.is_saved = True


class AgentRunner:
    def __init__(self, ctx: ChatContext, model: str, params: StreamGenerationParams, **kwargs):
        self.ctx : ChatContext = ctx
        self.model = model or appConfig.common.llm.model_name
        self.params = params
        self.kwargs = kwargs
        self.guardrail = None
        self.mcp_manager = MCPManager(params)
        self.agent = AgentService(self.mcp_manager)
        self.notebook_saver = None
        self.task_list = None
        self.kernel_manager = KernelManager(use_ray=env.EXECUTION_MODE_RAY)
        self.uncompleted_cells = {} # {id: cell}
        self.state = "ready"
        self.chunk_broadcasters = {}  # {record_id: deque()}
        self.logger = get_trace_logger(self.ctx)
        self.logger.info(f"AgentRunner {self} init model: {self.model}, params: {self.params}, kwargs: {self.kwargs}")

    def __str__(self):
        return f"AgentRunner({str(self.ctx)})"

    async def process_chunk(self, chunk: AgentEvent):
        await self.notebook_saver.chunk_to_notebook(chunk)
        await self._save_task_list(chunk)

    def get_broadcaster(self, record_id):
        if record_id not in self.chunk_broadcasters:
            self.chunk_broadcasters[record_id] = ChunkBroadcaster()
        return self.chunk_broadcasters[record_id]

    async def _save_task_list(self, chunk: AgentEvent):
        if chunk is None:
            return
        if chunk.event_type == "task_list":
            self.task_list = chunk
        if chunk.event_type == "studio_jupyter":
            # 从 metadata 中获取状态，如果没有则默认为 "running"
            status = chunk.metadata.get("status", "running") if chunk.metadata else "running"
            id = chunk.id
            if status == "running":
                self.uncompleted_cells[id] = chunk
            else:
                self.uncompleted_cells.pop(id, None)

    async def _get_finish_events(self):
        items = []
        for id, cell in self.uncompleted_cells.items():
            # 只设置 metadata 中的状态，不再设置主 status 字段
            if not cell.metadata:
                cell.metadata = {}
            cell.metadata["status"] = "error"
            items.append(cell)
        if self.task_list is None:
            return items
        task = copy.deepcopy(self.task_list.content)
        for idx in range(len(task.get("task_list", []))):
            cur_task = task["task_list"][idx]
            if cur_task.get("status") == "running":
                cur_task["status"] = "error"
            if len(cur_task.get("step_info_list", [])) > 0:
                for idx in range(len(cur_task.get("step_info_list", []))):
                    cur_step = cur_task["step_info_list"][idx]
                    if cur_step.get("status") == "running" or cur_step.get("status") == "pending":
                        cur_step["status"] = "error"
                    if "expand" in cur_step and cur_step.get("expand").get("status", "running") in ["running", "pending"]:
                        cur_step["expand"]["status"] = "error"
            logger.info(f"task_list after error: {task}")
        self.task_list.content = task
        items.append(self.task_list)
        return items

    async def _chat_agent(self, params: StreamGenerationParams):
        self.guardrail = Guardrail(params.ctx, prompt=params.question)
        self.mcp_manager = MCPManager(params)
        self.ctx = params.ctx

        async def chunk_generator(ctx, agent, params: StreamGenerationParams, mcp_manager):
            with use_span(ctx.parent_span, end_on_exit=True):
                self.logger.info(f"AgentRunner chunk_generator start, mcp_manager:{mcp_manager}, "
                                 f"knowledge_base_ids: {params.knowledge_base_ids}")
                async for chunk in agent.stream_chat(
                    ctx=ctx,
                    query=params.question,
                    session_id=ctx.get_session_id(),
                    record_id=params.record_id,
                    dataset_id=None,
                    chat_params=params,
                    mcp_manager=mcp_manager,
                ):
                    yield chunk
                self.logger.info(f"AgentRunner {self} chunk_generator end")
        self.guardrail.start_guard(chunk_generator, self.ctx, self.agent, params, self.mcp_manager)

    async def chat(self, params: StreamGenerationParams):
        self.uncompleted_cells = {}
        self.task_list = None
        self.notebook_saver = NotebookSaver(params)
        self.state = "running"
        await self._chat_agent(params)
        record_id = params.record_id

        broadcaster = self.get_broadcaster(params.record_id)
        async for chunk in self.guardrail.guard():
            if self.state == "stopped":
                self.logger.info("检测到停止请求，中断流式响应")
                break
            await self.jupyter_to_cos(params, chunk)
            await broadcaster.add_chunk(chunk)
            await self.process_chunk(chunk)
            yield chunk
        # 结束Guardrail流程
        self.logger.info(f"AgentRunner {self} chat guardrail stop, guardrail: {self.guardrail}")
        self.logger.info(f"AgentRunner {self} start send finish events")
        items = await self._get_finish_events()
        for item in items:
            await broadcaster.add_chunk(item)
            yield item
        await broadcaster.add_chunk("__END__")
        self.logger.info(f"AgentRunner {self} finish send finish events")
        await self.notebook_saver.close()
        await self.kernel_manager.session_stop(params.ctx.get_trace_id())
        self.state = "stopped"

    async def jupyter_to_cos(self, params, chunk):
        sub_account_uin = params.ctx.sub_account_uin
        session_id = params.ctx.session_id
        if chunk.event_type == "studio_jupyter":
            # 需要对图片上传cos
            jupyter_dict = json.loads(chunk.to_str())
            outputs = jupyter_dict.get("v", {}).get("outputs")
            jupyter_id = jupyter_dict.get("v", {}).get("id")
            if outputs:
                new_outputs = []
                for idx, output in enumerate(outputs):
                    if output.get("output_type") == "display_data":
                        data = output.get("data")  # data本身就是dict，不用json.loads
                        if data:
                            file_name = f"jupyter/{sub_account_uin}/{session_id}_{jupyter_id}_{idx}.json"
                            self.logger.info(f"jupyter_to_cos start, file_name: {file_name}")
                            try:
                                data_bytes = json.dumps(data, ensure_ascii=False).encode("utf-8")
                                cos_url = upload_byte_stream(file_name, data_bytes)
                                self.logger.info(f"jupyter_to_cos url: {cos_url}")
                                # 替换为带cos_url的输出
                                new_outputs.append({
                                    "metadata": {"cos_url": cos_url},
                                    "output_type": "display_data_cos"
                                })
                            except Exception as e:
                                self.logger.error(f"jupyter_to_cos upload error: {e}, file_name: {file_name}", exc_info=True)
                                # 出错时保留原output
                                new_outputs.append(output)
                        else:
                            # data为空，保留原output
                            new_outputs.append(output)
                    else:
                        # 非display_data类型，直接保留
                        new_outputs.append(output)
                # 赋值回jupyter_dict["v"]["outputs"]
                self.logger.info(f"jupyter_to_cos new new_outputs: {new_outputs}")
                chunk.update_outputs(new_outputs)

    async def wait_stop(self, timeout_millis: float = 1500):
        """等待Agent停止"""
        self.logger.info(f"AgentRunner {self} wait_stop start")
        start_time = time.time()
        while self.state != "stopped" and (time.time() - start_time) < timeout_millis / 1000:
            await asyncio.sleep(0.1)
        self.logger.info(f"AgentRunner {self} wait_stop success")
        return True

    async def stop(self):
        self.logger.info(f"AgentRunner {self} stop")
        if self.agent:
            self.agent.request_stop(self.ctx.get_session_id())
        if self.guardrail is not None:
            await self.guardrail.stop()
        if self.notebook_saver is not None:
            await self.notebook_saver.close()
        if self.kernel_manager is not None:
            await self.kernel_manager.session_stop(self.ctx.get_trace_id())
        logger.info(f"AgentRunner {self} stop success")
        return True

    async def get_chunks_for_resume(self, record_id):
        broadcaster = self.get_broadcaster(record_id)
        async for chunk in broadcaster.subscribe():
            yield chunk


class AgentManager:
    """管理Agent实例的工厂类，只负责路由不包含具体实现"""
    _cleanup_task = None

    @classmethod
    def _start_local_cleanup_task(cls):
        if EXECUTION_MODE != "ray" and cls._cleanup_task is None:
            loop = asyncio.get_event_loop()
            cls._cleanup_task = loop.create_task(cls._cleanup_inactive_local_agents())

    @classmethod
    async def _cleanup_inactive_local_agents(cls):
        while True:
            await asyncio.sleep(60)
            now = time.time()
            timeout = appConfig.common.time_out.actor_time_out  # 30分钟
            logger.info(f"[LocalAgentCleaner] 开始清理本地超时Agent，超时时间: {timeout}")
            inactive = [
                name for name, t in LocalAgent._last_active_times.items()
                if now - t > timeout
            ]
            for name in inactive:
                agent = LocalAgent._instances.pop(name, None)
                LocalAgent._last_active_times.pop(name, None)
                if agent:
                    await agent.stop()
                    logger.info(f"[LocalAgentCleaner] 清理本地超时Agent: {name}")
            # 打印当前所有存活的本地Agent
            alive_agents = list(LocalAgent._instances.keys())
            logger.info(f"[LocalAgentCleaner] 当前存活的本地Agent: {alive_agents}")

    @classmethod
    async def get_or_create_agent(cls, ctx: Context, model: str, params: StreamGenerationParams):
        """获取或创建Agent实例"""
        if EXECUTION_MODE == "ray":
            actor_name = f"{ctx.sub_account_uin}_{ctx.session_id}"
            try:
                logger.info(f"尝试获取现有RayAgent: {actor_name}")
                actor = ray.get_actor(actor_name, namespace="ray")
                logger.info(f"start to check RayAgent {actor_name} is ok")
                is_ok = await actor.is_ok.remote()
                if is_ok:
                    logger.info(f"RayAgent {actor_name} is ok: {is_ok}")
                    return actor
            except Exception as e:
                logger.info(f"创建RayAgent: {actor_name}, error: {e}")
                rst = RayAgent.options(
                    name=actor_name,
                    namespace="ray",
                    lifetime="detached",
                    max_concurrency=100,
                    num_cpus=15,  # 每个Actor分配1个CPU核心
                ).remote(ctx, model, params)
                logger.info(f"创建RayAgent: {actor_name} successfully created")
                return rst
        else:
            cls._start_local_cleanup_task()
            agent = await LocalAgent.get_or_create_agent(ctx, model, params)
            return agent

    @classmethod
    async def chat(cls, model, params: StreamGenerationParams):
        """执行聊天操作"""
        agent = await cls.get_or_create_agent(params.ctx, model, params)

        if EXECUTION_MODE == "ray":
            agent.chat.remote(params)
            while True:
                chunk = await agent.get_next_chunk.remote()
                if chunk is None:
                    await asyncio.sleep(0.1)
                    continue
                if chunk == "__STREAM_END__":
                    break
                yield chunk
        else:
            async for chunk in agent.chat(params):
                yield chunk

    @classmethod
    async def stop_agent(cls, actor_name: str):
        """停止指定Agent实例"""
        if EXECUTION_MODE == "ray":
            try:
                actor = ray.get_actor(actor_name, namespace="ray")
                logger.info(f"停止RayAgent: {actor_name}")
                await actor.stop.remote()
                logger.info(f"停止RayAgent: {actor_name} 成功")
                return True
            except Exception as e:
                logger.error(f"停止RayAgent失败: {e}")
                return False
        else:
            agent = LocalAgent._instances.pop(actor_name)
            if agent:
                return await agent.agent_stop()
            return False

    @classmethod
    async def get_chunks_for_resume(cls, ctx: Context, record_id):
        actor_name = f"{ctx.sub_account_uin}_{ctx.session_id}"
        if EXECUTION_MODE == "ray":
            actor = ray.get_actor(actor_name, namespace="ray")
            subscriber_id = await actor.start_resume_stream.remote(record_id)
            while True:
                chunk = await actor.get_next_resume_chunk.remote(record_id, subscriber_id)
                if chunk is None:
                    await asyncio.sleep(0.05)
                    continue
                if chunk == "__END__":
                    break
                yield chunk
        else:
            local_agent = LocalAgent._instances.get(actor_name)
            if local_agent:
                async for chunk in local_agent.agent_runner.get_chunks_for_resume(record_id):
                    if chunk == "__END__":
                        break
                    yield chunk


class ChunkBroadcaster:
    def __init__(self):
        self.history = deque()
        self.subscribers = set()
        self.lock = asyncio.Lock()  # 防止并发问题

    async def add_chunk(self, chunk):
        async with self.lock:
            self.history.append(chunk)
            for q in list(self.subscribers):
                await q.put(chunk)

    async def subscribe(self):
        # 1. replay history
        q = asyncio.Queue()
        async with self.lock:
            for chunk in self.history:
                await q.put(chunk)
            self.subscribers.add(q)
        try:
            while True:
                chunk = await q.get()
                yield chunk
                if chunk == "__END__":
                    break
        finally:
            async with self.lock:
                self.subscribers.discard(q)
