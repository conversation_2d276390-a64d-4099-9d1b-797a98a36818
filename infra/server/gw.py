import asyncio
import subprocess
import json
import ray
import uvicorn
import base64
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from infra.endpoint import chat_endpoint
from infra.knowledge_base.knowledge_base_ray import create_task_processor
from common.metric.server import start_metrics_server

from infra.mcp.nl2sql.generate import generate_sql
from infra.mcp.nl2sql.select_tables import select_tables

from infra.server import knowledge_base
from infra.server.middleware import setup_fastapi_middleware, lifespan
from infra.server import tools
from fastapi import FastAPI, Response, status, Request, BackgroundTasks
from infra.endpoint.agent import AgentManager
from infra.memory.chat_es_operator import ChatESOperator
from common.database.database import MysqlPool
from infra.adapter.user_info_adapter import UserInfoAdapter
from infra.adapter.user_session_adapter import UserSessionAdapter
from infra.domain.user_info_entity import UserInfo
from infra.memory.jupyter_operator import Jupyter<PERSON>perator
from common.share.config import appConfig
from common.share import env
from common.database.database import get_metadata_db_pool
from infra.mcp.nl2sql import generate
from infra.server.model import *
from infra.jupyter.manager import get_global_manager, init_global_manager
from common.metric.init_metric import init_all_metrics
app = FastAPI(lifespan=lifespan)
app.include_router(knowledge_base.router)
app.include_router(tools.router)

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=200,
        content=gw_error_json(400, ErrorCode.ParamError, str(exc))
    )
setup_fastapi_middleware(app)
# Global variable declaration
mysql_pool: MysqlPool = None


@app.post("/chat")
async def chat(chat_config: ChatConfig, request: Request, response: Response, background_tasks: BackgroundTasks):
    chat_config = chat_config.dict()
    ctx = request.state.context
    chat_manager = chat_endpoint.ChatManager(ctx)
    return await chat_manager.handle_chat(chat_config, background_tasks, response)


@app.post("/continue")
async def continue_chat(continue_config: ContinueConfig, request: Request, response: Response):
    continue_config = continue_config.dict()
    ctx = request.state.context
    chat_manager = chat_endpoint.ChatManager(ctx)
    return await chat_manager.continue_chat(continue_config, response)


@app.post("/chat/stop_stream")
def chat_stop_stream(stop_chat_config: StopChatConfig, request: Request, response: Response):
    """停止当前用户的聊天流"""
    ctx = request.state.context
    logger.info(f"chat_stop_stream ctx: {ctx}")
    sub_account_uin = ctx.sub_account_uin
    trace_id = ctx.trace_id
    stop_chat_config = stop_chat_config.dict()
    session_id = stop_chat_config["SessionId"]

    logger.info(f"停止聊天流 - SubAccountUin: {sub_account_uin}, SessionID: {session_id}, TraceID: {trace_id}")

    try:
        # 构造唯一的actor名称
        actor_name = f"{sub_account_uin}_{session_id}"

        # 尝试停止agent
        success = asyncio.run(AgentManager.stop_agent(actor_name))
        logger.info(f"停止聊天流 - SubAccountUin: {sub_account_uin}, SessionID: {session_id}, success: {success}")
        if success:
            return StopChatResponse(Status=status.HTTP_200_OK, Message="stop chat success")
        else:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            return gw_error(response.status_code, ErrorCode.InternalError, "Stop chat failed")

    except Exception as e:
        logger.error(f"停止聊天流失败: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error_json(response.status_code, ErrorCode.InternalError)


@app.post("/update_user_info")
def update_user(update_user_info: UpdateUserInfoConfig, request: Request, response: Response):
    """操作用户信息接口
    支持的操作类型:
    - query: 查询用户信息
    - create: 创建用户信息
    - modify: 修改用户信息
    - delete: 删除用户信息
    """
    ctx = request.state.context
    logger.info(f"update_user_info ctx: {ctx}")
    sub_account_uin = ctx.sub_account_uin
    trace_id = ctx.trace_id
    update_user_info = update_user_info.dict()
    jupyter_host = update_user_info["JupyterHost"]
    operate = update_user_info["Operate"]

    logger.info(f"操作用户信息 - SubAccountUin: {sub_account_uin}, Operate: {operate}, TraceID: {trace_id}")

    try:
        if operate == "query":
            # 查询操作
            ctx = request.state.context
            user_info_adapter = UserInfoAdapter(mysql_pool)
            user_info = user_info_adapter.get_by_sub_account_uin(ctx, sub_account_uin)
            if not user_info:
                response.status_code = status.HTTP_404_NOT_FOUND
                return gw_error_json(response.status_code, ErrorCode.NotFoundError, "user does not exist")
            return {
                "Status": status.HTTP_200_OK,
                "UserInfo": {
                    "SubAccountUin": str(user_info.sub_account_uin),
                    "JupyterHost": user_info.jupyter_host,
                    "CosUrl": user_info.cos_url,
                    "Description": user_info.description,
                    "CreatedAt": user_info.created_at.isoformat() if user_info.created_at else None,
                    "UpdatedAt": user_info.updated_at.isoformat() if user_info.updated_at else None
                }
            }

        else:
            success = True
            # 创建/修改/删除操作
            ctx = request.state.context
            user_info_adapter = UserInfoAdapter(mysql_pool)

            # 构造用户信息实体
            user_info = UserInfo(
                sub_account_uin=sub_account_uin,
                jupyter_host=jupyter_host,
            )

            # 执行操作
            if operate == "create":
                success = user_info_adapter.create_or_update(ctx, user_info)
            elif operate == "modify":
                # 先检查是否存在
                existing_info = user_info_adapter.get_by_sub_account_uin(ctx, sub_account_uin)
                if not existing_info:
                    response.status_code = status.HTTP_404_NOT_FOUND
                    return gw_error(response.status_code, ErrorCode.NotFoundError, "user does not exist")
                success = user_info_adapter.create_or_update(ctx, user_info)
            elif operate == "delete":
                # 先检查是否存在
                existing_info = user_info_adapter.get_by_sub_account_uin(ctx, sub_account_uin)
                if not existing_info:
                    response.status_code = status.HTTP_404_NOT_FOUND
                    return gw_error(response.status_code, ErrorCode.NotFoundError, "user does not exist")
                # 标记删除
                user_info.deleted_at = datetime.now()
                success = user_info_adapter.create_or_update(ctx, user_info)
            if not success:
                response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
                return gw_error(response.status_code, ErrorCode.InternalError, "operation failed")
            else:
                result = {
                    "Status": status.HTTP_200_OK,
                }
                return result

    except Exception as e:
        logger.error(f"操作用户信息失败: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "operation failed")


@app.post("/query_user_session_list")
def query_user_sessions(query_user_session_list: BaseModel, request: Request, response: Response):
    """获取用户所有未删除的会话列表"""

    ctx = request.state.context
    logger.info(f"query_user_sessions ctx: {ctx}")
    sub_account_uin = ctx.sub_account_uin
    trace_id = ctx.trace_id
    logger.info(f"获取用户会话列表 - SubAccountUin: {sub_account_uin}, trace_id: {trace_id}")

    try:
        ctx = request.state.context
        session_adapter = UserSessionAdapter(mysql_pool)
        sessions = session_adapter.get_all_by_user(ctx)
        result = {
            "Status": status.HTTP_200_OK,
            "SessionInfoList": [{
                "SessionId": session.session_id,
                "SessionTitle": session.session_title,
                "DbInfo": session.db_info,
                "CreateTime": session.created_at.isoformat() if session.created_at else None,
                "UpdateTime": session.updated_at.isoformat() if session.updated_at else None
            } for session in sessions]
        }
        return result
    except Exception as e:
        logger.error(f"获取用户会话列表失败: {e}")
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "get session list failed")


@app.post("/query_user_session_info")
def query_user_session_detail(query_user_session_info: QueryUserSessionInfoConfig, request: Request,
                              response: Response):
    query_user_session_info = query_user_session_info.dict()
    ctx = request.state.context
    logger.info(f"Request query_user_session_info: {query_user_session_info}, ctx: {ctx}")
    session_id = query_user_session_info.get("SessionId")
    sub_account_uin = ctx.sub_account_uin
    trace_id = ctx.trace_id

    session_adapter = UserSessionAdapter(mysql_pool)
    # 先检查会话是否存在
    user_session = session_adapter.get_by_id(ctx)
    if not user_session:
        response.status_code = status.HTTP_404_NOT_FOUND
        return gw_error(response.status_code, ErrorCode.NotFoundError, "chat session not found")
    run_record = getattr(user_session, "run_record", None)
    if not run_record:
        run_record = run_record
    try:
        chat_es_operator = ChatESOperator.get_instance(ctx.app_id)
        es_results = chat_es_operator.get_session_records(sub_account_uin, session_id)
        logger.info(f"ES查询结果: {es_results}, trace_id: {trace_id}")
    except Exception as e:
        logger.error(f"Error query_user_session_detail error : {e}, trace_id: {trace_id}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "query user session detail error")

    record_list = []
    for es_result in es_results:
        task_list_value = es_result.task_list
        if not task_list_value and es_result.task_list_dict:
            # 如果 task_list 是 None 或空，尝试用 task_list_dict
            # 如果 task_list_dict 不是字符串，转成 json 字符串
            if isinstance(es_result.task_list_dict, str):
                task_list_value = es_result.task_list_dict
            else:
                task_list_value = json.dumps(es_result.task_list_dict, ensure_ascii=False)
        record = {
            "SessionId": es_result.session_id,
            "SubAccountUin": sub_account_uin,
            "RecordId": es_result.record_id,
            "Context": es_result.context,
            "Answer": es_result.answer,
            "Question": es_result.question,
            "FinalSummary": es_result.final_summary,
            "Think": es_result.thinking_chain,
            "ErrorContext": es_result.error_context,
            "TaskList": task_list_value,  # 这里是json
            "DbInfo": es_result.db_info,  # 这里是json
            "CreateTime": es_result.create_time,
            "UpdateTime": es_result.update_time,
            "Feedback": es_result.feedback
        }
        record_list.append(record)
    logger.info(f"record_list: {record_list}, trace_id: {trace_id}")
    result = {
        "Status": status.HTTP_200_OK,
        "SubAccountUin": sub_account_uin,
        "SessionId": session_id,
        "RecordList": record_list,
        "RunRecord": run_record,
        "RecordCount": len(record_list)
    }
    return result


@app.post("/delete_data_agent_session")
def delete_data_agent(delete_data_agent_session: DeleteDataAgentSessionConfig, request: Request, response: Response):
    """删除用户会话(标记为已删除)"""
    delete_data_agent_session = delete_data_agent_session.dict()
    session_id = delete_data_agent_session["SessionId"]
    ctx = request.state.context
    logger.info(f"delete_data_agent ctx: {ctx}")
    sub_account_uin = ctx.sub_account_uin
    trace_id = ctx.trace_id

    logger.info(f"删除用户会话 - SubAccountUin: {sub_account_uin}, SessionID: {session_id}, TraceID: {trace_id}")

    try:
        ctx = request.state.context
        session_adapter = UserSessionAdapter(mysql_pool)

        # 先检查会话是否存在
        user_session = session_adapter.get_by_id(ctx)
        if not user_session:
            response.status_code = status.HTTP_404_NOT_FOUND
            return gw_error(response.status_code, ErrorCode.NotFoundError, "chat session not found")

        # 标记为已删除
        success = session_adapter.mark_as_deleted(ctx)
        if not success:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            return gw_error(response.status_code, ErrorCode.InternalError, "delete chat session failed")

        return {
            "Status": status.HTTP_200_OK
        }

    except Exception as e:
        logger.error(f"删除用户会话异常: {e}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "delete chat session failed")


@app.post("/add_feedback")
def add_feedback(add_feedback_config: AddFeedbackConfig, request: Request, response: Response):
    add_feedback_config = add_feedback_config.dict()
    record_id = add_feedback_config["RecordId"]
    feedback = add_feedback_config["Feedback"]
    ctx = request.state.context
    logger.info(f"add_feedback_config ctx: {ctx}")
    trace_id = ctx.trace_id

    logger.info(f"add_feedback - trace_id: {trace_id}, feedback: {feedback}")

    chat_es_operator = ChatESOperator.get_instance(ctx.app_id)
    success = chat_es_operator.update_feedback_by_record_id(record_id, feedback)
    if success:
        update_dict = {
            "record_id": record_id,
            "trace_id": trace_id,
            "feedback": feedback,
            "app_id": ctx.app_id,
        }
        success = generate.update_feedback(update_dict)
    if success:
        return {
            "Status": status.HTTP_200_OK
        }
    else:
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "update feedback failed")


class NL2SQLConfig(BaseModel):
    """自然语言转SQL接口参数"""
    app_id: str
    sub_account_uin: str
    trace_id: str
    data_engine_name: Optional[str] = None
    db_info:str
    is_sampling: bool = False  # 默认不采样
    mcp_url: dict  # mcp_url
    type: str
    question: str
    record_id:str


class SchemaLinkingConfig(BaseModel):
    """Schema Linking接口参数"""
    app_id: str
    sub_account_uin: str
    trace_id: str
    data_engine_name: Optional[str] = None
    db_info: str
    is_sampling: bool = False  # 默认不采样
    mcp_url: dict  # mcp_url
    type: str
    question: str
    record_id: str


@app.post("/nl2sql")
def nl2sql(params: NL2SQLConfig, request: Request, response: Response):
    """自然语言转SQL接口
    参数格式与其他接口保持统一，使用Pydantic模型验证
    """
    try:
        logger.info(f"nl2sql请求参数 - {params.model_dump()}")
        # 直接使用已验证的参数，移除了硬编码测试数据
        rsp = generate_sql(params.model_dump())
        return {
            "Status": status.HTTP_200_OK,
            "data": rsp
        }
    except ValidationError as e:
        logger.error(f"参数验证失败: {str(e)}",exc_info=True)
        response.status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        return gw_error(response.status_code, ErrorCode.ParamError)
    except Exception as e:
        logger.error(f"SQL生成异常: {str(e)}",exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError)


@app.post("/select_tables")
def nl2sql_select_tables(params: SchemaLinkingConfig, request: Request, response: Response):
    """Schema Linking接口
    根据自然语言问题分析相关的数据库列
    """
    try:
        ctx = request.state.context
        logger.info(f"nl2sql_select_tables 请求参数 - {params.model_dump()}, trace_id: {ctx.trace_id}")
        
        # 调用外部方法处理Schema Linking
        result = select_tables(params.model_dump())
        
        logger.info(f"schema_linking处理完成 - trace_id: {ctx.trace_id}")
        
        return {
            "Status": status.HTTP_200_OK,
            "data": result
        }
    except ValidationError as e:
        logger.error(f"Schema Linking参数验证失败: {str(e)}", exc_info=True)
        response.status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        return gw_error(response.status_code, ErrorCode.ParamError)
    except Exception as e:
        logger.error(f"Schema Linking处理异常: {str(e)}", exc_info=True)
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError)


@app.post("/query_expand")
def query_expand(query_expand_info: QueryExpandInfoConfig, request: Request, response: Response):
    query_expand_req = query_expand_info.dict()
    logger.info(f"Request query_expand_req: {query_expand_req}")
    ctx = request.state.context
    logger.info(f"query_expand ctx: {ctx}")
    sub_account_uin = ctx.sub_account_uin
    trace_id = ctx.trace_id
    session_id = query_expand_req.get("SessionId")
    record_id = query_expand_req.get("RecordId")
    cell_id = query_expand_req.get("CellId")

    try:
        jupyter_operator = JupyterOperator.get_instance(ctx.app_id)
        es_result = jupyter_operator.get_jupyter_records(sub_account_uin, session_id, record_id, cell_id)
        logger.info(f"ES jupyter 查询结果: {es_result}, trace_id: {trace_id}")
    except Exception as e:
        logger.error(f"Error query_expand error : {e}, trace_id: {trace_id}")
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return gw_error(response.status_code, ErrorCode.InternalError, "Query expand error")

    result = {}
    if es_result:
        jupyter = es_result[0].jupyter
        cell_type = jupyter.get("cell_type", "")

        try:
            outputs_json = json.dumps(jupyter)
            outputs_bytes = outputs_json.encode('utf-8')
            outputs_base64 = base64.b64encode(outputs_bytes)
            outputs_base64_str = outputs_base64.decode('utf-8')
        except Exception as e:
            logger.error(f"Base64编码失败: {str(e)}")
            outputs_base64_str = ""  # 编码失败时返回空字符串
        result = {
                "Status": status.HTTP_200_OK,
                "CellId": cell_id,
                "CellType": cell_type,
                "CellData": outputs_base64_str
            }

    return result

if __name__ == "__main__":
    logger.info("Starting Data Agent Gateway")
    mysql_pool = get_metadata_db_pool()
    if env.EXECUTION_MODE == "ray":
        ray_address = appConfig.common.ray_config.ray_address  # 指定 Ray 集群的 IP 地址
        logger.info(f"Ray address: {ray_address}")
        python_path = "/ray/" + env.IMAGE_TAG
        logger.info(f"ray python_path :{python_path}")
        ray.init(
            namespace="ray",
            address=f"ray://{ray_address}",
            log_to_driver=False,
            runtime_env={"env_vars": {
                "OTEL_SDK_DISABLED": "true",
                "LANGSMITH_OTEL_ENABLED": "false",
                "LANGSMITH_TRACING": "false",
                "MEM0_TELEMETRY": "False",
                "IMAGE_TAG": env.IMAGE_TAG,
                "CONF_PATH": python_path + "/etc/config.yaml",
                "LOG_PATH": "/tmp/ray/session_latest/logs",
                "WORKING_DIR": python_path,
                "EXECUTION_MODE": env.EXECUTION_MODE,
                "OMP_NUM_THREADS": "5",
                "RAY_memory_usage_threshold": "0.98",  # 提高OOM阈值
                "PYTHONPATH": python_path,  # 运行环境目录
                "LOGGER_TO_STDOUT": "true",
            },
                "py_modules": ["common", "infra"],
                "pip": appConfig.common.ray_config.dependencies,
            }
        )
        logger.info("Connected to Ray cluster!")
        nodes = ray.nodes()
        for node in nodes:
            logger.info(f"Node: {node['NodeManagerAddress']}, Alive: {node['Alive']}, Resources: {node['Resources']}")

        if env.IMAGE_TAG:
            logger.info(f"env image tag: {env.IMAGE_TAG}")
            subprocess.run(['sh', 'common/ray/copy_file.sh', env.IMAGE_TAG], check=True)
    start_metrics_server()

    if env.START_AISEARCH_TASK:
        # start_task_executor(mysql_pool)
        create_task_processor().start()
    init_global_manager()
    init_all_metrics()
    uvicorn.run(app, host="0.0.0.0", port=8791)
