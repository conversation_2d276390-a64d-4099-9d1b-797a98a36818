from common.share.error import ErrorCode, error_msg
from pydantic import field_validator
from typing import List, Optional, Literal, Type, Any
from peewee import Expression, Model
from datetime import datetime
from pydantic import BaseModel, ValidationError, conint
from common.logger.logger import logger


class BaseUserSessionConfig(BaseModel):
    SessionId: str


class ChatConfig(BaseUserSessionConfig):
    Question: str
    Model: str = ""
    DeepThinking: bool = True
    DataSourceIds: List[str] = []
    KnowledgeBaseIds: List[str] = []
    AgentType: str = ""
    Context: str = ""
    OldRecordId: str = ""


class ContinueConfig(BaseUserSessionConfig):
    RecordId: str = ""


class StopChatConfig(BaseUserSessionConfig):
    pass


class UpdateUserInfoConfig(BaseModel):
    JupyterHost: str
    Operate: Literal['query', 'create', 'modify', 'delete']


class QueryUserSessionInfoConfig(BaseUserSessionConfig):
    pass


class DeleteDataAgentSessionConfig(BaseUserSessionConfig):
    pass


class QueryExpandInfoConfig(BaseUserSessionConfig):
    RecordId: str
    CellId: str


# 操作符约束（限制可选值）
FilterOperator = Literal["eq", "ne", "gt", "lt", "ge", "le", "in", "like"]


# 过滤条件
class Filter(BaseModel):
    Field: str
    Op: FilterOperator
    Value: List[str]

    def to_peewee_condition(self, model: Type[Model], db_field_map: dict) -> Expression:
        db_field = db_field_map.get(self.Field)
        if db_field is None:
            logger.error(f"不支持的字段: {self.Field}")
            raise ValueError(f"服务不支持该字段查询: {self.Field}")
        if not hasattr(model, db_field):
            logger.error(f"模型 {model.__name__} 不存在字段: {db_field}")
            raise ValueError(f"模型 {model.__name__} 不存在字段: {db_field}")
        field = getattr(model, db_field)

        # 4. 生成 Peewee 条件表达式
        return self._build_condition(field, self.Op, self.Value)

    @staticmethod
    def _build_condition(field, op: str, parsed_values: List[Any]) -> Expression:
        """根据操作符生成条件表达式"""
        operator_map = {
            "eq": lambda: field == parsed_values[0],
            "ne": lambda: field != parsed_values[0],
            "gt": lambda: field > parsed_values[0],
            "lt": lambda: field < parsed_values[0],
            "ge": lambda: field >= parsed_values[0],
            "le": lambda: field <= parsed_values[0],
            "in": lambda: field.in_(parsed_values),
            "like": lambda: field % f"%{parsed_values[0]}%",  # 模糊匹配
        }
        if op not in operator_map:
            raise ValidationError(f"不支持的操作符: {op}")
        if op in ["eq", "ne", "gt", "lt", "ge", "le", "like"] and len(parsed_values) != 1:
            raise ValidationError(f"操作符 {op} 需要1个值，当前提供了 {len(parsed_values)} 个")
        return operator_map[op]()


# 排序条件
class Sort(BaseModel):
    Field: str
    Desc: bool = False  # 默认正序

    def to_peewee_order(self, model: Type[Model], db_field_map: dict) -> Expression:
        db_field = db_field_map.get(self.Field)
        if db_field is None:
            logger.error(f"不支持的字段: {self.Field}")
            raise ValidationError(f"服务不支持该字段查询: {self.Field}")
        if not hasattr(model, db_field):
            logger.error(f"模型 {model.__name__} 不存在字段: {db_field}")
            raise ValidationError(f"模型 {model.__name__} 不存在字段: {db_field}")
        field = getattr(model, db_field)
        # 生成排序表达式（升序/降序）
        return field.desc() if self.Desc else field.asc()


# 接口请求参数
class RequestKnowledgeParams(BaseModel):
    Filters: Optional[List[Filter]] = None
    Sorts: Optional[List[Sort]] = None
    KnowledgeBaseId: str = "default"
    Page: conint(gt=0) = 1  # 页码（从1开始）
    PageSize: conint(gt=0) = 10  # 每页数量（最大100）

class ChunkConfig(BaseModel):
    ChunkType: int
    MaxChunkSize: int
    Delimiters: Optional[List[str]]= None
    ChunkOverlap: int = 0

# 知识库信息项
class KnowledgeInfo(BaseModel):
    FileId: str
    FileName: str = None
    FileSize: float
    FileUrl: str = None
    Status: int  # 0:处理中;1:可用;-1:错误
    Type: int  # 0:文本
    CreateUser: str
    ChunkConfig: ChunkConfig
    Source: Optional[int] = 0
    IsShowCase: Optional[int] = 0
    CreateTime: datetime  # ISO 8601 时间格式
    class Config:
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }

KNOWLEDGE_TYPE_MAP = {
    "FileId": "file_id",
    "FileName": "file_name",
    "FileSize": "file_size",
    "Status": "status",
    "Type": "type",
    "CreateUser": "create_user",
    "CreateTime": "create_time",
}



# 错误信息（仅失败时返回）
class ErrorInfo(BaseModel):
    Message: Optional[str] = None
    Code: Optional[str] = None


# 接口响应数据
class KnowledgeResponse(BaseModel):
    Status: int  # 200:成功;500:失败
    KnowledgeInfoList: List[KnowledgeInfo] = []
    Error: Optional[ErrorInfo] = None
    Total: int = 0


class AddFeedbackConfig(BaseModel):
    RecordId: str
    Feedback: int

class Document(BaseModel):
    FileName: str
    FileId: str
    FileType: str
    FileUrl: str
    FileSize: float
    IsShowCase: Optional[int] = 0
    Source: Optional[int] = 0

class KnowledgeTaskBatch(BaseModel):
    KnowledgeBaseId: str = "default"
    KnowledgeBaseName: str = "default"
    Documents :List[Document]
    Config: ChunkConfig


class KnowledgeTask(BaseModel):
    FileUrl: str
    Config: ChunkConfig

class UpdateKnowledgeTask(BaseModel):
    FileId: str
    Config: ChunkConfig

# 公共响应数据，可继承
class CommonResponse(BaseModel):
    Status: int
    Error: Optional[ErrorInfo] = None


class RequestChunkListParams(BaseModel):
    FileId: str
    Content: Optional[str]=None
    Page: conint(gt=0) = 1  # 页码（从1开始）
    PageSize: conint(gt=0) = 10  # 每页数量（最大100）

class ChunkInfo(BaseModel):
    Id: str
    Content: str
    Size: str

class ChunkListResponse(CommonResponse):
    ChunkList: List[ChunkInfo] = []
    Total: int = 0


class ChunkModify(BaseModel):
    FileId: str
    ChunkId: str
    Content: str

    @field_validator('Content')
    def content_not_empty(cls, v):
        if not v.strip():
            raise ValueError("Content cannot be empty")
        return v


class ChunkDelete(BaseModel):
    FileId: str
    ChunkIds: List[str]

class FileDelete(BaseModel):
    FileIds: List[str]


class FileUpdate(BaseModel):
    FileId: str
    FileName: str

    @field_validator('FileName')
    def file_name_validate(cls, v):
        if not v.strip():
            raise ValueError("Content cannot be empty")
        if len(v.strip()) > 30:
            raise ValueError("File name cannot be longer than 30 characters")
        return v

class ChunkCreate(BaseModel):
    FileId: str
    Content: str
    BeforeChunkId: Optional[str] = None
    AfterChunkId: Optional[str] = None
    InsertPos: Optional[int] = None

    @field_validator('Content')
    def content_not_empty(cls, v):
        if not v.strip():
            raise ValueError("Content cannot be empty")
        return v

class KnowledgeSearch(BaseModel):
    Query: str


class KnowledgeSearchResponse(CommonResponse):
    Contents: List[str] = []


class SearchConfig(BaseModel):
    Type: int
    Num: int
    EmbeddingWeight: float
    Rerank: int

class QueryKnowledgeConfig(BaseModel):
    pass

class ModifyKnowledgeConfig(BaseModel):
    SearchConfig: SearchConfig

class QueryKnowledgeConfigResponse(BaseModel):
    Status: int
    SearchConfig: SearchConfig
    Error: Optional[ErrorInfo] = None

class ModifyKnowledgeConfigResponse(BaseModel):
    Status: int
    Error: Optional[ErrorInfo] = None

class StopChatResponse(BaseModel):
    Status: int
    Message: str

class SemanticConfig(BaseModel):
    TermId: str
    Term: str
    Definition: str
    Synonyms: List[str]
    Scope: List[str]

class QuerySemanticListParams(BaseModel):
    Page: int = 1
    PageSize: int = 10

class QuerySemanticListResponse(BaseModel):
    Status: int
    SemanticConfigList: List[SemanticConfig] = []
    Error: Optional[ErrorInfo] = None
    Total: int = 0

class ModifySemanticParams(BaseModel):
    TermId: str
    Term: Optional[str] = None
    Definition: Optional[str] = None
    Synonyms: Optional[List[str]] = None
    Scope: Optional[List[str]] = None

    @field_validator('Term')
    def term_not_empty(cls, v):
        if not v.strip():
            raise ValueError("Term cannot be empty")
        return v
    @field_validator('Definition')
    def definition_not_empty(cls, v):
        if not v.strip():
            raise ValueError("Definition cannot be empty")
        return v


class AddSemanticParams(BaseModel):
    Term: str
    Definition: str
    Synonyms: Optional[List[str]] = None
    Scope: Optional[List[str]] = ["*.*.*"]

    @field_validator('Term')
    def term_not_empty(cls, v):
        if not v.strip():
            raise ValueError("Term cannot be empty")
        return v

    @field_validator('Definition')
    def definition_not_empty(cls, v):
        if not v.strip():
            raise ValueError("Definition cannot be empty")
        return v


class DeleteSemanticParams(BaseModel):
    TermIds: List[str]


def gw_error(status: int, err_code: ErrorCode, err_message: str = None) -> CommonResponse:
    '''
       usage:
       return gw_error(response.status_code, ErrorCode.ParamError)
       return gw_error(response.status_code, "CONFIG_ERROR")
       return gw_error(response.status_code, "CONFIG_ERROR", "config error")
       return gw_error(response.status_code, ErrorCode.InternalError, "internal error, please try again later or contact admin")
    '''
    return CommonResponse(Status=status, Error=ErrorInfo(Code=err_code, Message=err_message if err_message else error_msg(err_code)))

def gw_error_json(status: int, err_code: ErrorCode, err_message: str = None) -> dict:
    return gw_error(status, err_code, err_message).model_dump_json()

if __name__ == "__main__":
    print(gw_error_json(400, ErrorCode.ParamError))
    print(gw_error_json(400, "CONFIG_ERROR"))