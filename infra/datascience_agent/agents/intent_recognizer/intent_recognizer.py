# datascience_multi_agent/agents/intent_recognizer/intent_recognizer.py
import json
import logging
from typing import Dict, Any, Optional, List, TYPE_CHECKING, AsyncIterator, Union
import asyncio
import re

from common.trace.trace import traceable, add_event, set_status
from .intent_schemas import IntentSlot, TaskInfo, IntentUpdateToolArgs
from .prompt import get_system_prompt_with_context, JSON_SEPARATOR, THINK_SEPARATOR, get_output_prompt
from common.elements.agent_event import ThinkEvent, TextEvent, MessageEvent
from infra.guardrail.language_identification import LanguageIdentifier
import traceback
if TYPE_CHECKING:
    from infra.datascience_agent.utils.openai_client import OpenAIClient

logger = logging.getLogger(__name__)


class IntentRecognizer:
    def __init__(self, llm_client: 'OpenAIClient', initial_slot_data: Optional[Dict[str, Any]] = None):
        self.llm_client = llm_client
        self.language_identifier = LanguageIdentifier(languages=['zh', 'en'])
        self._final_user_facing_text = ""  # 存储最终的完整消息文本
        if initial_slot_data:
            self.intent_slot = IntentSlot(**initial_slot_data)
        else:
            self.intent_slot = IntentSlot()
        
    def detect_user_language(self, user_input: str) -> str:
        """检测用户输入的语言"""
        try:
            return self.language_identifier.identify_language(user_input)
        except Exception as e:
            logger.debug(f"语言检测失败: {e}, 使用默认语言 'zh'")
            return 'zh'  # 默认中文
        

    @traceable(name="process_user_input_stream")
    async def process_query(self, user_input: str, conversation_history: List[Dict[str, str]] = None, ctx: Optional[Dict[str, Any]] = None, state: Optional[Dict[str, Any]] = None) -> AsyncIterator[Union[ThinkEvent, TextEvent, MessageEvent]]:
        """
        流式处理用户输入，返回ThinkEvent（思考过程）、TextEvent或MessageEvent事件流
        现在使用外部传入的conversation_history而不是自己维护历史记录
        """
        if not user_input:
            logger.warning("User input is empty. No action taken.")
            yield TextEvent(content="请告诉我您想要做什么？")
            return

        # 🌐 每次输入都检测语言并存储
        user_language = self.detect_user_language(user_input)
        self._detected_language = user_language
        logger.info(f"🌐 IntentRecognizer: 检测到用户语言: {user_language}")
        
        # 从state中读取database_schema并设置到dataset
        if state and state.get('database_schema'):
            self.intent_slot.dataset = state['database_schema']
            logger.info(f"从state加载database_schema到intent_slot.dataset: {type(state['database_schema'])}")
        else:
            logger.info(f"state中未找到database_schema")

        # 从ctx中读取深度思考模式设置
        deep_thinking = ctx.get_deep_thinking() if ctx else True

        logger.info(f"深度思考模式: {'开启' if deep_thinking else '关闭'}")

        # 更新对话计数（仅在当前层增加）
        self.intent_slot.content.conversation_count += 1
        logger.info(f"Last Layer: {self.intent_slot.content.layer}, conversation {self.intent_slot.content.conversation_count}")

        # 正常任务结束后重置，方便测试，新 Query 进来时重置
        if self.intent_slot.content.layer == 3:
            self.intent_slot.update_slot({"layer": 1})
            logger.info(f"Update Layer: {self.intent_slot.content.layer}, conversation {self.intent_slot.content.conversation_count}")

        # 使用外部传入的conversation_history构建消息
        messages_for_llm = self._build_llm_messages(user_input, conversation_history or [], deep_thinking, user_language, ctx)
        logger.info(f"intent_llm_input messages: {messages_for_llm}")
        
        user_facing_text_parts = []
        json_payload_parts = []
        think_parts = []
        final_user_facing_text = ""  # 初始化防止UnboundLocalError
        
        # 流式处理状态 - 新的顺序：JSON -> THINKING -> MESSAGE
        capturing_json = True  # 开始时捕获JSON
        capturing_thinking = False
        capturing_message = False
        buffer = ""
        
        separator_len = len(JSON_SEPARATOR)
        think_separator_len = len(THINK_SEPARATOR)

        try:
            async for chunk in self.llm_client.generate_stream(messages_for_llm):
                if not chunk.choices or not chunk.choices[0].delta:
                    continue
                
                delta = chunk.choices[0].delta
                
                if hasattr(delta, 'content') and delta.content:
                    content_delta = delta.content
                    buffer += content_delta

                    # 状态1: 捕获JSON内容
                    if capturing_json:
                        # 查找JSON分隔符
                        separator_index = buffer.find(JSON_SEPARATOR)
                        if separator_index != -1:
                            # 找到JSON分隔符，JSON部分结束
                            json_content = buffer[:separator_index]
                            if json_content:
                                json_payload_parts.append(json_content)
                            
                            # --- JSON 接收完成，立刻处理和更新槽位 ---
                            json_payload_str = "".join(json_payload_parts).strip()
                            if json_payload_str:
                                try:
                                    parsed_json_payload = self._parse_json_payload(json_payload_str)
                                    if parsed_json_payload:
                                        logger.info(f"intent_llm_output: {parsed_json_payload}")
                                        # 验证JSON结构
                                        IntentUpdateToolArgs(**parsed_json_payload)
                                        
                                        # 应用层次转换逻辑并更新槽位
                                        parsed_json_payload = self._apply_layer_transition_logic(parsed_json_payload)
                                        self.intent_slot.update_slot(parsed_json_payload)
                                        logger.info(f"槽位更新完成: layer={self.intent_slot.content.layer}, count={self.intent_slot.content.conversation_count}")
                                except Exception as e:
                                    logger.error(f"JSON处理错误: '{json_payload_str}'. 错误: {e}", exc_info=True)
                                    yield TextEvent(content="[系统提示: 处理内部状态更新时遇到问题，请重试或重新表述。]")
                            
                            # 切换到思考状态
                            capturing_json = False
                            if deep_thinking:
                                capturing_thinking = True
                            else:
                                capturing_message = True
                            
                            # 处理分隔符后的内容
                            buffer = buffer[separator_index + separator_len:]
                        else:
                            # 还在JSON中，继续收集
                            continue

                    # 状态2: 捕获THINKING内容（仅在深度思考模式下）
                    elif capturing_thinking:
                        # 查找思考分隔符
                        think_separator_index = buffer.find(THINK_SEPARATOR)
                        if think_separator_index != -1:
                            # 找到思考分隔符，思考部分结束
                            think_content = buffer[:think_separator_index]
                            if think_content:
                                # 输出剩余的思考内容（如果有的话）
                                yield ThinkEvent(content=think_content)
                                think_parts.append(think_content)
                            
                            # 发送思考结束分隔符
                            yield TextEvent(content="")
                            
                            # 切换到消息状态
                            capturing_thinking = False
                            capturing_message = True
                            
                            # 处理分隔符后的内容
                            buffer = buffer[think_separator_index + think_separator_len:]
                        else:
                            # 还在思考中，流式输出思考内容
                            if len(buffer) > think_separator_len:
                                # 输出部分思考内容，保留可能的分隔符
                                yield_think = buffer[:-think_separator_len]
                                if yield_think:
                                    yield ThinkEvent(content=yield_think)
                                    think_parts.append(yield_think)
                                buffer = buffer[-think_separator_len:]
                            continue

                    # 状态3: 捕获MESSAGE内容
                    elif capturing_message:
                        # 在消息状态下，流式输出所有内容
                        if buffer:
                            yield MessageEvent(content=buffer)
                            user_facing_text_parts.append(buffer)
                            buffer = ""

            # 流结束，处理剩余的缓冲区内容
            if buffer:
                if capturing_json:
                    # JSON阶段不应该在流结束时还有剩余内容，因为JSON有明确的分隔符
                    # 如果到这里说明JSON可能格式异常，直接忽略
                    logger.warning(f"流结束时仍在JSON捕获状态，忽略剩余内容: '{buffer}'")
                elif capturing_thinking:
                    # 思考未完成，只输出剩余的思考内容（buffer部分）
                    if buffer.strip():
                        yield ThinkEvent(content=buffer)
                        think_parts.append(buffer)
                    yield TextEvent(content="")  # 思考结束分隔符
                elif capturing_message:
                    # 消息内容
                    yield MessageEvent(content=buffer)
                    user_facing_text_parts.append(buffer)

            # 记录最终的用户面向文本
            final_user_facing_text = "".join(user_facing_text_parts).strip()
            self._final_user_facing_text = final_user_facing_text  # 保存到实例变量
            logger.info(f"intent_llm_output messages: {final_user_facing_text}")

        except Exception as e:
            logger.error(f"流处理错误: {e},traceback: {traceback.format_exc()}")
            yield TextEvent(content="处理您的请求时遇到意外错误，请重试。")

    def _parse_json_payload(self, json_payload_str: str) -> Optional[Dict[str, Any]]:
        """解析JSON负载，支持处理包含markdown代码块的JSON"""
        # 首先尝试移除可能的markdown代码块标记
        cleaned_payload = json_payload_str.strip()
        
        # 移除markdown代码块的标记
        # 匹配 ```json...``` 或 ```...``` 格式
        markdown_pattern = r'```(?:json)?\s*\n?(.*?)\n?```'
        markdown_matches = re.findall(markdown_pattern, cleaned_payload, re.DOTALL)
        
        if markdown_matches:
            # 使用第一个匹配到的代码块内容
            cleaned_payload = markdown_matches[0].strip()
            logger.debug(f"从markdown代码块中提取JSON: '{cleaned_payload}'")
        
        # 查找第一个 '{' 和最后一个 '}' 来提取JSON
        start_brace = cleaned_payload.find('{')
        end_brace = cleaned_payload.rfind('}')

        if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
            actual_json_to_parse = cleaned_payload[start_brace : end_brace + 1]
            logger.debug(f"解析JSON: '{actual_json_to_parse}'")
            
            # 尝试解析JSON，如果遇到"Extra data"错误，只取第一个完整的JSON对象
            try:
                return json.loads(actual_json_to_parse)
            except json.JSONDecodeError as e:
                if "Extra data" in str(e):
                    # 当遇到额外数据错误时，尝试只解析到第一个完整的JSON对象
                    lines = actual_json_to_parse.split('\n')
                    brace_count = 0
                    json_lines = []
                    
                    for line in lines:
                        json_lines.append(line)
                        # 计算大括号的层次
                        brace_count += line.count('{') - line.count('}')
                        
                        # 当大括号层次归零时，说明找到了完整的JSON对象
                        if brace_count == 0 and json_lines[0].strip().startswith('{'):
                            first_json = '\n'.join(json_lines)
                            logger.debug(f"提取第一个完整JSON对象: '{first_json}'")
                            return json.loads(first_json)
                    
                    # 如果仍然无法解析，抛出原始错误
                    raise e
                else:
                    # 其他JSON错误，直接抛出
                    raise e
        else:
            logger.warning(f"未找到有效的JSON对象: '{cleaned_payload}'")
            # 尝试分段解析
            potential_segments = cleaned_payload.split(JSON_SEPARATOR)
            for segment in potential_segments:
                segment = segment.strip()
                # 同样处理可能的markdown代码块
                if '```' in segment:
                    markdown_matches = re.findall(markdown_pattern, segment, re.DOTALL)
                    if markdown_matches:
                        segment = markdown_matches[0].strip()
                
                if segment.startswith("{") and segment.endswith("}"):
                    logger.debug(f"分段解析找到JSON: '{segment}'")
                    return json.loads(segment)
            
            raise json.JSONDecodeError("No valid JSON object found", cleaned_payload, 0)

    def _apply_layer_transition_logic(self, json_payload: Dict[str, Any]) -> Dict[str, Any]:
        """应用层次转换逻辑"""
        # 确保conversation_count在层次转换时重置
        if "layer" in json_payload:
            current_layer = self.intent_slot.content.layer
            new_layer = json_payload["layer"]
            
            if new_layer != current_layer and new_layer in [2, 3]:
                if "conversation_count" not in json_payload:
                    json_payload["conversation_count"] = 0
                    logger.info(f"层次转换: {current_layer} -> {new_layer}, 重置对话计数为0")
        
        return json_payload

    def _build_llm_messages(self, user_input: str, conversation_history: List[Dict[str, str]], deep_thinking: bool = False, user_language: str = 'zh', ctx=None) -> List[Dict[str, str]]:
        """构建LLM对话消息"""
        # 获取主要系统提示（现在包含对话历史和当前输入）
        system_prompt_content = get_system_prompt_with_context(
            self.intent_slot, 
            deep_thinking, 
            user_language,
            ctx
        )
        
        # # 获取当前层指引
        # layer_guidance_prompt = get_prompt_for_layer(
        #     self.intent_slot,
        #     user_input,
        #     deep_thinking,
        #     user_language
        # )
        #
        # # 合并系统提示，避免多个system角色消息
        # if layer_guidance_prompt:
        #     combined_system_prompt = f"{system_prompt_content}\n\n---\n\n**当前层指引：**\n{layer_guidance_prompt}"
        # else:
        #     combined_system_prompt = system_prompt_content

        combined_system_prompt = system_prompt_content

        messages = [{"role": "system", "content": combined_system_prompt}]
        messages.extend(conversation_history)
        messages.append({"role": "user", "content": user_input})

        return messages

    def get_current_slot_state(self) -> Dict[str, Any]:
        """获取当前槽位状态的详细信息"""
        return self.intent_slot.model_dump()
    
    def get_detected_language(self) -> Optional[str]:
        """获取检测到的用户语言"""
        return getattr(self, '_detected_language', None)
    

    def is_intent_recognition_complete(self) -> bool:
        """检查意图识别是否完成"""
        is_complete = self.intent_slot.content.layer == 3
        
        # 添加详细调试日志
        logger.info(f"🔍 Intent Recognition Complete Check - Layer: {self.intent_slot.content.layer}, Task: {self.intent_slot.content.task}")
        
        # 检查是否是结束对话的特殊任务
        if (is_complete and self.intent_slot.content.task and 
            (isinstance(self.intent_slot.content.task, dict) and 
             self.intent_slot.content.task.get("category") == "end_conversation" or
             hasattr(self.intent_slot.content.task, "category") and 
             self.intent_slot.content.task.category == "end_conversation")):
            logger.info("意图识别完成: 第三层到达，任务类型为结束对话")
            return True
        
        if is_complete:
            logger.info("意图识别完成: 第三层到达")
            return True
        
        logger.info(f"意图识别未完成: Layer={self.intent_slot.content.layer}")
        return False

    def reset_intent(self):
        """重置意图识别器"""
        self.intent_slot.reset_for_new_intent()
        logger.info("IntentRecognizer 已重置")

    # 辅助方法
    def get_current_layer(self) -> int:
        """获取当前层次"""
        return self.intent_slot.content.layer

    def get_conversation_count(self) -> int:
        """获取当前层的对话轮次"""
        return self.intent_slot.content.conversation_count

    def get_task_category(self) -> Optional[str]:
        """获取任务类别"""
        if self.intent_slot.content.task:
            return self.intent_slot.content.task.category
        return None

    def get_essential_info(self) -> Dict[str, Any]:
        """获取必要信息"""
        return self.intent_slot.content.essential_info

    def get_supplementary_info(self) -> Dict[str, Any]:
        """获取补充信息"""
        return self.intent_slot.content.supplementary_info

    def force_next_layer(self):
        """强制进入下一层（用于测试或特殊情况）"""
        current_layer = self.intent_slot.content.layer
        if current_layer < 3:
            self.intent_slot.update_slot({
                "layer": current_layer + 1,
                "conversation_count": 0
            })
            logger.info(f"强制进入第{current_layer + 1}层")

    def can_proceed_to_next_layer(self) -> bool:
        """检查是否可以进入下一层 - 现在由LLM完全控制"""
        # 层次转换现在完全由LLM的JSON输出控制
        # 这个方法保留用于向后兼容，但总是返回True
        return True

    def get_layer_summary(self) -> str:
        """获取当前状态的摘要"""
        layer = self.intent_slot.content.layer
        count = self.intent_slot.content.conversation_count
        task_category = self.get_task_category() or "未识别"
        
        if layer == 1:
            return f"第一层-必要信息识别 ({count}/3轮) | 任务类别: {task_category}"
        elif layer == 2:
            return f"第二层-补充信息收集 ({count}/1轮) | 任务类别: {task_category}"
        elif layer == 3:
            return f"第三层-确认完成 | 任务类别: {task_category}"
        else:
            return f"未知层次: {layer}"

    def get_final_user_facing_text(self) -> str:
        """获取最终的完整用户面向文本"""
        return self._final_user_facing_text