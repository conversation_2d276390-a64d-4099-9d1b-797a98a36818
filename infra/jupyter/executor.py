# 标准库导入
import os
import io
import sys
import re
import base64
from abc import ABC, abstractmethod
from contextlib import redirect_stdout, redirect_stderr
from typing import Dict, Any, List, Tuple
from IPython.display import display, Markdown
from enum import Enum
from dataclasses import dataclass
from IPython.core.displayhook import DisplayHook
from IPython.core.displaypub import DisplayPublisher
# 第三方库导入
from IPython.core.interactiveshell import InteractiveShell
from nbformat.v4 import new_output as nb_output
from datetime import datetime
# 本地导入
from common.logger.logger import logger

class BaseExecutor(ABC):
    """Notebook代码执行器基类"""
    @abstractmethod
    def execute(self, code: str):
        pass

    @abstractmethod
    def execute_multiple(self, codes: List[str]):
        pass

    @abstractmethod
    def get_cell_outputs(self, codes: List[str]):
        pass

    @abstractmethod
    def shutdown(self):
        pass

class OutputType(Enum):
    """输出类型枚举"""
    FIGURE = "figure"
    DISPLAY_DATA = "display"
    STDOUT = "stdout"
    STDERR = "stderr"
    IGNORE = "ignore"

@dataclass
class ExecutionOutput:
    """统一的执行输出结构体"""
    output_type: OutputType
    content: Any
    timestamp: datetime = datetime.now()

class MyDisplayHook(DisplayHook):
    collector = lambda x: None
    def __init__(self, shell=None, *args, **kwargs):
        super().__init__(shell=shell, *args, **kwargs)
    
    def write_format_data(self, format_dict, md_dict=None):
        MyDisplayHook.collector(format_dict)
        return super().write_format_data(format_dict, md_dict)

class MyDisplayPublisher(DisplayPublisher):
    '''
    自定义DisplayPublisher，用于收集display()产生的输出
    '''
    collector = lambda x: None
    def __init__(self, shell=None, *args, **kwargs):
        super().__init__(shell=shell, *args, **kwargs)

    def publish(self, data, **kwargs):
        MyDisplayPublisher.collector(data)
        super().publish(data,**kwargs)

class RealTimeCapture(io.StringIO):
    """实时捕获输出的类"""
    def __init__(self, output_type: OutputType, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.output_type = output_type
    
    def pop_content(self):
        content = self.getvalue()
        self.seek(0)
        self.truncate(0)
        return content


# IPythonExecutor继承BaseExecutor
class IPythonExecutor(BaseExecutor):
    """基于IPython shell的notebook输出收集执行器（仿JupyterExecutor风格）"""
    def __init__(self, pythonpath: str = None):
        """
        初始化IPython执行器
        
        Args:
            pythonpath: Python路径，可以是单个字符串或字符串列表，用于设置IPython的sys.path
            working_dir: 工作目录，用于设置IPython的工作目录
        """
        # 设置Python路径
        collector =  lambda x: self._add_exec_output(
            ExecutionOutput(
                output_type=OutputType.DISPLAY_DATA,
                content=x,
            )
        )
        MyDisplayPublisher.collector = collector
        MyDisplayHook.collector = collector
        self.add_pythonpath(pythonpath)
        InteractiveShell.clear_instance()
        InteractiveShell.display_pub_class = MyDisplayPublisher
        InteractiveShell.displayhook_class = MyDisplayHook
        self.ipython = InteractiveShell.instance()
        # self.ipython.displayhook_class = CollectingDisplayHook
        self.execution_count = self.ipython.execution_count
        self.logger = logger

    def _initialize_matplotlib(self):
        try:
            import matplotlib.pyplot as plt
            import matplotlib as mpl
            orig_font_family = mpl.rcParams.get('font.family', [])
            if isinstance(orig_font_family, str):
                orig_font_family = [orig_font_family]
            if 'Noto Sans SC' not in orig_font_family:
                mpl.rcParams['font.family'] = ['Noto Sans SC'] + orig_font_family 
            mpl.rcParams['figure.autolayout'] = True  # 自动调用 tight_layout
            # mpl.rcParams['figure.subplot.top'] = 0.8
            plt.close('all')
            return plt
        except Exception:
            return None

    def _start_new_execution(self):
        self.execution_count = self.ipython.execution_count
        self.exec_outputs = []
        self.stdout_capture = RealTimeCapture(OutputType.STDOUT)
        self.stderr_capture = RealTimeCapture(OutputType.STDERR)

    def _flush_exec_outputs(self):
        stdout_content = self.stdout_capture.pop_content()
        stderr_content = self.stderr_capture.pop_content()
        if len(stdout_content) > 0:
            self.exec_outputs.append(
                ExecutionOutput(
                    output_type=OutputType.STDOUT,
                    content=stdout_content,
                )
            )
        if len(stderr_content) > 0:     
            self.exec_outputs.append(
                ExecutionOutput(
                    output_type=OutputType.STDERR,
                    content=stderr_content,
                )
            )
   
    def _add_exec_output(self, output: ExecutionOutput):
        self._flush_exec_outputs()
        self.exec_outputs.append(output)
        # 清理  stdout_capture 和 stderr_capture 中的内容
        
    
    def execute(self, code: str, store_history: bool = True) -> Tuple[dict, bool]:
        # Capture stdout 、stderr、figs 、display_data
        self._start_new_execution()
        result = None
        self.logger.info(f"IPythonExecutor execute: {code}")

        plt = self._initialize_matplotlib()
        if plt:
            original_show = plt.show
            def _capture_show(*args, **kwargs):
                current_figs = [plt.figure(n) for n in plt.get_fignums()]
                for fig in current_figs:
                    try:
                        buf = io.BytesIO()
                        # bbox_inches='tight' 参数用于让保存的图片内容紧密地包裹住所有的图形元素，去除多余的空白边缘。
                        # pad_inches='layout' 参数用于设置图片边缘的填充宽度，这里设置为'layout'可以让matplotlib自动根据布局调整边距。
                        fig.savefig(buf, format='png', bbox_inches='tight', pad_inches='layout')
                        buf.seek(0)
                        self._add_exec_output(
                            ExecutionOutput(
                                output_type=OutputType.FIGURE,
                                content=(repr(fig),base64.b64encode(buf.read()).decode('utf-8')),
                            )
                        )
                        plt.close(fig)
                    except Exception:
                        pass
                return original_show(*args, **kwargs)
            plt.show = _capture_show
        with redirect_stdout(self.stdout_capture), redirect_stderr(self.stderr_capture):
            try:
                # Execute the code
                result = self.ipython.run_cell(code, store_history=store_history)
                if plt:
                    plt.close('all')
            except Exception as e:
                logger.error(f"Error executing code: {e}")
            finally:
                if plt:
                    plt.show = original_show
                self.execution_count = self.ipython.execution_count
                self._flush_exec_outputs()

        self.logger.info(f"IPythonExecutor execute finish, error_in_exec: {result.error_in_exec}, exec_outputs: {self.exec_outputs}")
        rst = self._process_exec_results_new(self.exec_outputs, None if result is None else result.error_in_exec)    
        rst = self._format_output(rst)
        self.logger.info(f"IPythonExecutor execute success, rst: {rst}, error_in_exec: {result.error_in_exec}")
        return rst, result.error_in_exec is not None
    
    
    def _process_exec_results_new(self, exec_outputs, error_in_exec):
        """
        使用新的统一结构体数组处理执行结果
        exec_outputs: List[ExecutionOutput] - 按时间顺序排列的统一输出结构体数组
        error_in_exec: Exception - 执行过程中的错误
        """
        outputs = []
        
        # 0. 处理ignore输出
        for idx, output in enumerate(exec_outputs):
            # 如果text/plain的值等于下一个stdout的值，则下一个stdout不输出
            if isinstance(output.content, dict) and 'text/plain' in output.content:
                if idx + 1 < len(exec_outputs) and exec_outputs[idx + 1].output_type == OutputType.STDOUT:
                    old_stdout = exec_outputs[idx + 1].content
                    replace_str = output.content['text/plain'].strip()
                    if isinstance(old_stdout, str):
                        old_stdout = old_stdout.replace(replace_str, '').lstrip()
                    print(f"old_stdout: {old_stdout}, replace_str: {replace_str}")
                    if old_stdout.strip() == '':
                        exec_outputs[idx + 1].output_type = OutputType.IGNORE
                        continue
                    else:
                        exec_outputs[idx + 1].content = old_stdout

        # 1. 处理错误情况
        if error_in_exec is not None:
            self._process_error_output_new(error_in_exec, exec_outputs, outputs)
            return {'outputs': outputs, 'execution_count': self.execution_count}
        
        # 2. 按顺序处理所有输出
        for output in exec_outputs:
            if output.output_type == OutputType.IGNORE:
                continue
            elif output.output_type == OutputType.STDOUT:
                self._process_stdout_output_new(output, outputs)
            elif output.output_type == OutputType.STDERR:
                self._process_stderr_output_new(output, outputs)
            elif output.output_type == OutputType.FIGURE:
                self._process_figure_output_new(output, outputs)
            elif output.output_type == OutputType.DISPLAY_DATA:
                self._process_display_data_output_new(output, outputs)
        
        return {'outputs': outputs, 'execution_count': self.execution_count}
    
    def _process_error_output_new(self, error_in_exec, exec_outputs, outputs):
        """处理错误输出（新版本）"""
        import traceback
        
        # 先处理错误前的stdout输出
        stdout_before_error = []
        for output in exec_outputs:
            if output.output_type == OutputType.STDOUT:
                stdout_before_error.append(output.content)
        
        if stdout_before_error:
            # 合并所有stdout内容
            combined_stdout = ''.join(stdout_before_error)
            # 过滤掉错误信息
            lines = combined_stdout.split('\n')
            error_start = -1
            for i, line in enumerate(lines):
                if ('Traceback (most recent call last)' in line or 
                    'NameError' in line or 'TypeError' in line or 'ValueError' in line or
                    '---------------------------------------------------------------------------' in line):
                    error_start = i
                    break
            
            if error_start >= 0:
                clean_stdout = '\n'.join(lines[:error_start])
                if combined_stdout.endswith('\n') and not clean_stdout.endswith('\n'):
                    clean_stdout += '\n'
            else:
                clean_stdout = combined_stdout
            
            if clean_stdout.strip():
                outputs.append(nb_output('stream', name='stdout', text=[clean_stdout]))
        
        # 输出错误信息
        try:
            structured_tb = self.ipython.InteractiveTB.structured_traceback(
                type(error_in_exec), error_in_exec, error_in_exec.__traceback__)
            notebook_traceback = list(structured_tb)
        except Exception:
            tb_list = traceback.format_exception(type(error_in_exec), error_in_exec, error_in_exec.__traceback__)
            notebook_traceback = ['-' * 75] + [line.rstrip('\n') for line in tb_list if line.strip()]
        
        outputs.append(nb_output('error', ename=type(error_in_exec).__name__, evalue=str(error_in_exec), traceback=notebook_traceback))
    
    def _process_stdout_output_new(self, output, outputs):
        """处理stdout输出（新版本）"""
        import re
        
        content = output.content
        if not content.strip():
            return
        has_final_newline = content.endswith('\n')
        
        # 过滤 Out[x]: 行
        lines = content.split('\n')
        filtered_lines = []
        for line in lines:
            if re.match(r'^Out\[\d+\]:\s*', line):
                has_final_newline = True
                continue
            filtered_lines.append(line)
        filtered_stdout = '\n'.join(filtered_lines)
        if has_final_newline and not filtered_stdout.endswith('\n'):
            filtered_stdout += '\n'
        
        if filtered_stdout.strip():
            outputs.append(nb_output('stream', name='stdout', text=[filtered_stdout]))
    
    def _process_stderr_output_new(self, output, outputs):
        """处理stderr输出（新版本）"""
        content = output.content
        if content.strip():
            outputs.append(nb_output('stream', name='stderr', text=[content]))
    
    def _process_figure_output_new(self, output, outputs):
        """处理图片输出（新版本）"""
        content = output.content
        metadata = {}
        
        data = {}
        data['image/png'] = content[1]
        data['text/plain'] = [content[0]]
        outputs.append(nb_output('display_data', data=data, metadata=metadata))
    
    def _process_display_data_output_new(self, output, outputs):
        """处理display数据输出（新版本）"""
        content = output.content
        metadata = {}
        
        if not isinstance(content, dict):
            self.logger.warning(f"display_data content is not a dict: {content}")
            return
        
        data = {}
        for mime_type, content_value in content.items():
            if isinstance(content_value, list):
                data[mime_type] = content_value
            elif isinstance(content_value, str):
                data[mime_type] = [content_value]
            else:
                data[mime_type] = [str(content_value)]
        
        # 判断是 execute_result 还是 display_data
        if 'text/plain' in data:
            # 只有 text/plain，可能是 execute_result
            outputs.append(nb_output('execute_result', data=data, metadata={}, execution_count=self.execution_count))
        else:
            # 有富媒体内容，是 display_data
            outputs.append(nb_output('display_data', data=data, metadata={}))

    # def _process_execution_result(self, display_datas, stdout_content, stderr_content, figs_captured, error_in_exec):
    #     """Process execution result with original logic"""
    #     outputs = []
        
    #     # 1. 判断主要的output_type
    #     if error_in_exec is not None:
    #         # output_type: error
    #         self._process_error_output(error_in_exec, stdout_content, outputs)
    #     elif figs_captured:
    #         # output_type: display_data
    #         self._process_display_data_output(display_datas, figs_captured, outputs)
    #         return {'outputs': outputs, 'execution_count': self.execution_count}
    #     elif display_datas:
    #         # output_type: execute_result
    #         self._process_execute_result_output(display_datas, stdout_content , outputs)
    #         return {'outputs': outputs, 'execution_count': self.execution_count}
        
    #     # 2. output_type: stream
    #     if stdout_content and error_in_exec is None:
    #         self._process_stdout_output(stdout_content, outputs)
        
    #     # 3. output_type: error
    #     if stderr_content:
    #         self._process_stderr_output(stderr_content, outputs)
        
    #     return {'outputs': outputs, 'execution_count': self.execution_count}

    # def _process_error_output(self, error_in_exec, stdout_content, outputs):
    #     """处理错误输出"""
    #     import traceback
        
    #     # 如果有stdout内容，过滤掉错误信息
    #     if stdout_content.strip():
    #         lines = stdout_content.split('\n')
    #         error_start = -1
    #         for i, line in enumerate(lines):
    #             if ('Traceback (most recent call last)' in line or 
    #                 'NameError' in line or 'TypeError' in line or 'ValueError' in line or
    #                 '---------------------------------------------------------------------------' in line):
    #                 error_start = i
    #                 break
            
    #         if error_start >= 0:
    #             clean_stdout = '\n'.join(lines[:error_start])
    #             if stdout_content.endswith('\n') and not clean_stdout.endswith('\n'):
    #                 clean_stdout += '\n'
    #         else:
    #             clean_stdout = stdout_content
            
    #         if clean_stdout.strip():
    #             outputs.append(nb_output('stream', name='stdout', text=[clean_stdout]))
        
    #     # 输出错误信息
    #     try:
    #         structured_tb = self.ipython.InteractiveTB.structured_traceback(
    #             type(error_in_exec), error_in_exec, error_in_exec.__traceback__)
    #         notebook_traceback = list(structured_tb)
    #     except Exception:
    #         tb_list = traceback.format_exception(type(error_in_exec), error_in_exec, error_in_exec.__traceback__)
    #         notebook_traceback = ['-' * 75] + [line.rstrip('\n') for line in tb_list if line.strip()]
        
    #     outputs.append(nb_output('error', ename=type(error_in_exec).__name__, evalue=str(error_in_exec), traceback=notebook_traceback))

    # def _process_display_data_output(self, display_datas, figs_captured, outputs):
    #     """处理display_data输出（主要是matplotlib图片）"""
    #     if display_datas:
    #         for display_data in display_datas:
    #             if not isinstance(display_data, dict):
    #                 logger.warning(f"display_data is not a dict: {display_data}")
    #                 continue
    #             data = {}
    #             for mime_type, content in display_data.items():
    #                 if isinstance(content, list):
    #                     data[mime_type] = content
    #                 elif isinstance(content, str):
    #                     data[mime_type] = [content]
    #                 else:
    #                     data[mime_type] = [str(content)]
    #             outputs.append(nb_output('display_data', data=data, metadata={}))
    #     for img_data in figs_captured:
    #         data = {}
    #         data['image/png'] = img_data[1]
    #         data['text/plain'] = [str(img_data[0])] if 'text/plain' not in data else data['text/plain'] + [str(img_data[0])]
    #         outputs.append(nb_output('display_data', data=data, metadata={}))

    # def _process_execute_result_output(self, display_datas, stdout_content, outputs):
    #     """处理execute_result输出"""
    #     try:
    #         new_stdout_content = stdout_content
    #         for display_data in display_datas:
    #             if 'text/plain' in display_data:
    #                 new_stdout_content = new_stdout_content.replace(str(display_data['text/plain']), '')
    #         # 使用捕获的display_data
    #         if len(new_stdout_content.strip()) > 0:
    #             # 过滤 Out[x]: 行
    #             lines = new_stdout_content.split('\n')
    #             stream_text = []
    #             stream_finished = False
    #             for line in lines:
    #                 if re.match(r'^Out\[.*]:\s*', line):
    #                     stream_finished = True
    #                     break
    #                 if re.match(r'^Out\[\d+\]:\s*', line):
    #                     stream_finished = True
    #                     break
    #                 if not stream_finished:
    #                     stream_text.append(line + '\n')
    #             if len(stream_text) > 0:
    #                 outputs.append(nb_output('stream', name='stdout', text=stream_text))
    #         data = {}
    #         for display_data in display_datas:
    #             if not isinstance(display_data, dict):
    #                 logger.warning(f"display_data is not a dict: {display_data}")
    #                 continue
    #             for mime_type, content in display_data.items():
    #                 if isinstance(content, list):
    #                     data[mime_type] = content
    #                 else:
    #                     data[mime_type] = [content]
    #             outputs.append(nb_output('execute_result', data=data, metadata={}, execution_count=self.execution_count))
    #     except Exception:
    #         outputs.append(nb_output('execute_result', data={'text/plain': [repr(result.result)]}, metadata={}, execution_count=self.execution_count))

    # def _process_stdout_output(self, stdout_content, outputs):
    #     """处理stdout stream输出"""
    #     import re
        
    #     # 过滤 Out[x]: 行
    #     lines = stdout_content.split('\n')
    #     filtered_lines = []
    #     for line in lines:
    #         if re.match(r'^Out\[\d+\]:\s*', line):
    #             continue
    #         filtered_lines.append(line)
    #     filtered_stdout = '\n'.join(filtered_lines)
        
    #     if filtered_stdout.strip():
    #         outputs.append(nb_output('stream', name='stdout', text=[filtered_stdout]))

    # def _process_stderr_output(self, stderr_content, outputs):
    #     """处理stderr stream输出"""
    #     if stderr_content.strip():
    #         outputs.append(nb_output('stream', name='stderr', text=[stderr_content]))

    def _format_output(self, output_dict):
        # print(output_dict)
        def format_output(input_str) -> list:
            if not isinstance(input_str, str) and not isinstance(input_str, list):
                return input_str
            input_list = [input_str] if isinstance(input_str, str) else input_str
            has_final_newline = input_list[-1].endswith('\n')
            output_list = []
            for sub_str in input_str:
                has_final_newline = sub_str.endswith('\n')
                output_list.extend([line + '\n' if line.strip() else line + '\n' for line in sub_str.rstrip().split('\n')])
            if not has_final_newline and output_list[-1].endswith('\n'):
                output_list[-1] = output_list[-1][:-1]
            return output_list
        
        for output in output_dict['outputs']:
            if output['output_type'] == 'stream':
                output['text'] = format_output(output['text'])
            if 'data' in output:
                for key, value in output['data'].items():
                    if key in ['text/html', 'text/markdown', 'text/plain']:
                        output['data'][key] = format_output(value)
        return output_dict
        
    def execute_multiple(self, codes: List[str]) -> Tuple[List[dict], bool]:
        results = []
        for code in codes:
            result, has_error = self.execute(code)
            results.append(result)
            if has_error:
                return results, True
        return results, False

    def get_cell_outputs(self, codes: List[str]) -> List[dict]:
        return self.execute_multiple(codes)

    def add_pythonpath(self, pythonpath: str):
        if pythonpath:
            for path in pythonpath.strip().split(':') if isinstance(pythonpath, str) else pythonpath:
                if os.path.exists(path) and path not in sys.path:
                    sys.path.insert(0, path) 
                    logger.info(f"Added to Python path: {path}")

    def status_ok(self) -> bool:
        return True
    
    def shutdown(self):     
        pass  # IPython shell无需特殊关闭

        
class JupyterExecutor(BaseExecutor):
    """基于Jupyter kernel的notebook原生输出收集执行器"""
    def __init__(self):
        self.km = None
        self.kc = None
        self._initialize_kernel()

    def _initialize_kernel(self):
        """初始化kernel"""
        try:
            from jupyter_client import KernelManager
            self.km = KernelManager()
            self.km.start_kernel()
            self.kc = self.km.client()
            self.kc.start_channels()
            self.kc.wait_for_ready()
            self.execution_count = 1
        except Exception as e:
            self.shutdown()
            raise RuntimeError(f"Failed to initialize kernel: {e}")

    def execute(self, code: str) -> Dict[str, Any]:
        if not self.kc:
            raise RuntimeError("Kernel not initialized")
        
        try:
            self.kc.execute(code)
            outputs = []
            while True:
                msg = self.kc.get_iopub_msg(timeout=10)
                msg_type = msg['header']['msg_type']
                if msg_type == 'status' and msg['content']['execution_state'] == 'idle':
                    break
                if msg_type in ('execute_result', 'display_data', 'stream', 'error'):
                    outputs.append(msg)
            
            rst_outputs = []
            for msg in outputs:
                content = msg['content']
                msg_type = msg['header']['msg_type']
                if msg_type == 'stream':
                    rst_outputs.append(nb_output('stream', name=content['name'], text=content['text']))
                elif msg_type == 'execute_result':
                    rst_outputs.append(nb_output('execute_result', data=content['data'], metadata=content['metadata'], execution_count=content['execution_count']))
                elif msg_type == 'display_data':
                    rst_outputs.append(nb_output('display_data', data=content['data'], metadata=content['metadata']))
                elif msg_type == 'error':
                    rst_outputs.append(nb_output('error', ename=content['ename'], evalue=content['evalue'], traceback=content['traceback']))
            
            result = {
                'outputs': rst_outputs,
                'execution_count': self.execution_count
            }
            self.execution_count += 1
            return result
        except Exception as e:
            # 如果执行出错，尝试重新初始化kernel
            self.shutdown()
            self._initialize_kernel()
            raise RuntimeError(f"Execution failed: {e}")

    def execute_multiple(self, codes: List[str]) -> List[Dict[str, Any]]:
        """
        执行多个代码块
        Args:
            codes: 代码块列表
        Returns:
            notebook格式的输出列表
        """
        results = []
        for code in codes:
            result = self.execute(code)
            results.append(result)
        return results

    def get_cell_outputs(self, codes: List[str]) -> List[Dict[str, Any]]:
        """
        获取多个代码块的notebook格式输出
        Args:
            codes: 代码块列表
        Returns:
            notebook格式的输出列表
        """
        return self.execute_multiple(codes)

    def shutdown(self):
        """安全关闭kernel连接"""
        try:
            if self.kc:
                self.kc.stop_channels()
                self.kc = None
        except Exception:
            pass
        
        try:
            if self.km:
                self.km.shutdown_kernel()
                self.km = None
        except Exception:
            pass

    def __del__(self):
        """析构函数，确保资源被正确清理"""
        self.shutdown()

if __name__ == '__main__':
    # 基本功能演示
    print("=== 基本功能演示 ===")
    executor = IPythonExecutor()
    result = executor.execute('a=1\nb=2\nc=a+b\nc')
    print(result)
    # os._exit(0)
    # 测试markdown功能
    print("\n=== 测试markdown功能 ===")
    result = executor.execute('print("Hello World")\nfrom IPython.display import display, Markdown\n'
                              'info = """\n'
                              '## 数据概览\n'
                              '### 基本信息\n'
                              '- **数据形状**: 2 行 × 2 列\n'
                              '- **内存使用**: 0.00 MB\n'
                              '\n'
                              '### 数据类型\n'
                              '- **object**: 1 列\n'
                              '- **int64**: 1 列\n'
                              '\n'
                              '### 列信息\n'    
                              '- `a` • `b`\n'
                              '\n'
                              '### 数据样例\n'
                              '\n'
                              '"""\n'
                              'display(Markdown(info))\n'
                              'print("Hello World")')
    # os._exit(0)

    # 测试pandas功能
    print("\n=== 测试pandas功能 ===")
    result = executor.execute('import pandas as pd\n'
                                'df = pd.DataFrame({"a": [1, 2], "b": [3, 4]})\n'
                                'print(f"数据读取到了 DataFrame 变量 df")\n'
                                'print(f"数据实例:")\n'
                                'df.head()')
    print(result)
    os._exit(0)

    # 测试matplotlib功能
    print("\n=== 测试matplotlib功能 ===")
    result = executor.execute('''
import matplotlib.pyplot as plt
import numpy as np
# 方法2：强制设置显示大小
fig = plt.figure(figsize=(1, 0.8), dpi=100)
theta = np.linspace(0, 2 * np.pi, 100)
x = np.cos(theta)
y = np.sin(theta)
plt.plot(x, y, linewidth=0.5)
plt.title("Circle", fontsize=6)
plt.tight_layout(pad=0.05)
print("Circle Image")
plt.show()
print("Forecast for next 2 months:")''')
    print(result)
    
    executor.shutdown() 