
from common.share import env
import ray
import time
import asyncio
import os
import subprocess
import shutil
from typing import List, Tu<PERSON>, Dict, Any
from infra.jupyter.basic import KernelURN, KernelClient, get_logger
from infra.jupyter.executor import IPythonExecutor
@ray.remote
class RayIPythonExecutor(IPythonExecutor):
    def __init__(self, urn: KernelURN):
        super().__init__()
        self.urn = urn
        self.logger = get_logger(urn, trace_id=urn.kernel_id)
        self.logger.info(f"RayIPythonExecutor init success, urn: {urn}")
        self.last_active = time.time()
        self.timeout = 15 * 60
        try:
            asyncio.get_event_loop().create_task(self._self_cleanup())
        except Exception:
            pass

    def execute(self, code: str, timeout: int = None) -> Tuple[List[Dict[str, Any]], bool]:
        try:
            return super().execute(code, timeout)
        except Exception as e:
            self.logger.error(f"RayIPythonExecutor execute error: {e}")
            raise e
        finally:
            self.last_active = time.time()
            
    async def _self_cleanup(self):
        self.logger.info(f"[RayIPythonExecutor] start !")
        while True:
            if time.time() - self.last_active > self.timeout:
                self.logger.warning(f"[RayIPythonExecutor] 超时自杀: last_active={self.last_active}, now={time.time()}, timeout={self.timeout}")
                os._exit(0)
            else:
                self.logger.info(f"[RayIPythonExecutor] 检查自杀: last_active={self.last_active}, now={time.time()}, timeout={self.timeout}")
            await asyncio.sleep(60)

class IpythonKernelClientRay(KernelClient):
    def __init__(self, urn: KernelURN):
        super().__init__(urn)
        self.logger = get_logger(urn, trace_id=urn.kernel_id)
        self.ipython_kernel = None
        self.ray_actor_name = f"ipython_{self.urn.sub_uin}_{self.urn.kernel_id}"

    def start_kernel(self):
        if self.ipython_kernel:
            return self.urn
        try:
            actor = ray.get_actor(self.ray_actor_name, namespace="ray")
            if ray.get(actor.status_ok.remote()):
                self.ipython_kernel = actor
                self.logger.info(f"IpythonKernelClientRay get existed kernel success: {actor}")
            else:
                self.logger.info(f"IpythonKernelClientRay get existed kernel error: {actor}")
                raise Exception("IpythonKernelClientRay get existed kernel error: {actor}")
            return self.urn
        except Exception as e:
            self.logger.info(f"IpythonKernelClientRay get existed kernel error: {e}, start new kernel")
            self.ipython_kernel = RayIPythonExecutor.options(
                name=self.ray_actor_name, lifetime="detached",
                max_concurrency=100,
                namespace="ray",
                num_cpus=15,
            ).remote(self.urn)
        return self.urn
    
    def kernel_status_ready(self) -> bool:
        try:
            return ray.get_actor(self.ray_actor_name) is not None
        except Exception as e:
            self.logger.error(f"Kernel status not ready: {e}")
            return False

    def exists_kernel(self, kernel_name: str = None, kernel_id: str = None):
        try:
            actor = ray.get_actor(self.ray_actor_name)
            return [{'id': self.urn.kernel_id, 'name': kernel_name or self.urn.kernel_name}]
        except Exception:
            return []

    def execute(self, code: str, timeout: int = None) -> Tuple[List[Dict[str, Any]], bool]:
        self.start_kernel()
        if not self.ipython_kernel:
            self.logger.error(f"IpythonKernelClientRay execute error: ipython_kernel is None")
            raise Exception("IpythonKernelClientRay execute error: ipython_kernel is None")
        result, has_error = ray.get(self.ipython_kernel.execute.remote(code, timeout))
        return result['outputs'], has_error
    
    def add_lib_path(self, path: str):
        self.start_kernel()
        if self.ipython_kernel:
            ray.get(self.ipython_kernel.add_pythonpath.remote(path))
        else:
            self.logger.error(f"IpythonKernelClientRay add_lib_path error: ipython_kernel is None")
            raise RuntimeError("IpythonKernelClientRay add_lib_path error: ipython_kernel is None")
        
    def shutdown(self):
        try:
            ray.kill(self.ipython_kernel)
            self.logger.info(f"Shutdown ipython kernel success")
        except Exception as e:
            self.logger.error(f"Shutdown ipython kernel error: {e}")

class IpythonKernelClientLocal(KernelClient):
    global_ipython_kernel = None
    
    def __init__(self, urn: KernelURN):
        super().__init__(urn)
        self.logger = get_logger(urn, trace_id=urn.kernel_id)
        self.ipython_kernel: IPythonExecutor = IpythonKernelClientLocal.global_ipython_kernel
    
    def start_kernel(self):
        self.logger.info(f"Start ipython kernel")
        IpythonKernelClientLocal.global_ipython_kernel = IPythonExecutor()
        self.ipython_kernel = IpythonKernelClientLocal.global_ipython_kernel
        # 为ipython内核生成一个唯一的kernel_id
        if not self.urn.has_kernel_id():
            import uuid
            self.urn.kernel_id = f"ipython_{uuid.uuid4().hex[:8]}"
        return self.urn
    
    def kernel_status_ready(self) -> bool:
        self.logger.info(f"Check ipython kernel status: {self.ipython_kernel is not None}")
        return self.ipython_kernel is not None

    def exists_kernel(self, kernel_name: str = None, kernel_id: str = None):
        if self.ipython_kernel:
            return [{'id': self.urn.kernel_id, 'name': self.urn.kernel_name}]
        return []

    def add_lib_path(self, path: str):
        self.logger.info(f"Add lib path: {path}")
        self.ipython_kernel.add_pythonpath(path)

    def execute(self, code: str, timeout: int = None) -> Tuple[List[Dict[str, Any]], bool]:
        result, has_error = self.ipython_kernel.execute(code, timeout)
        return result['outputs'], has_error
    
    def shutdown(self):
        self.ipython_kernel.shutdown()
        self.logger.info(f"Shutdown ipython kernel success")

class IpythonKernelClient(KernelClient):
    def __init__(self, urn: KernelURN):
        super().__init__(urn)
        self.venv_path = env.CFS_PATH + "/venv"
        self.cache_path = env.CFS_PATH + "/cache"
        self.lib_path = None
        self.logger = get_logger(urn, trace_id=urn.kernel_id)
        self.client = IpythonKernelClientRay(urn) if env.EXECUTION_MODE_RAY else IpythonKernelClientLocal(urn)
        self.logger.info(f"IpythonKernelClient init success, venv_path: {self.venv_path}, cache_path: {self.cache_path}")
    
    def start_kernel(self):
        self.logger.info(f"Start ipython kernel")
        return self.client.start_kernel()
    
    def kernel_status_ready(self) -> bool:
        self.logger.info(f"Check ipython kernel status: {self.client.kernel_status_ready()}")
        return self.client.kernel_status_ready()

    def exists_kernel(self, kernel_name: str = None, kernel_id: str = None):
        self.logger.info(f"Check ipython kernel exists: {kernel_name}, {kernel_id}")
        return self.client.exists_kernel(kernel_name, kernel_id)

    def install_requirements(self, requirements: List[str]) -> Tuple[List[Dict[str, Any]], bool]:
        self.logger.info(f"Install ipython kernel requirements: {requirements}")
        if len(requirements) == 0:
            return [], False
        if not self.init_venv():
            self.logger.error(f"Init venv error")
            return [{"error": "init venv error"}], True
        
        output, has_error = self.execute_in_subprocess(f"source {os.path.join(self.venv_path, 'bin/activate')} && python -c \"import sysconfig;print(sysconfig.get_paths()['platlib']+':'+sysconfig.get_paths()['purelib'])\"")
        if has_error:
            self.logger.error(f"Get lib path error: {output}")
            return [{"error": "get lib path error"}], True
        self.lib_path = output['stdout'].strip()
        # 按要求用 pip install xxx -t
        outputs = []
        has_error = False
        for requirement in requirements:
            # 优先使用 venv 下的 pip，如果存在
            venv_pip = os.path.join(self.venv_path, "bin/pip")
            if os.path.exists(venv_pip):
                pip_cmd = f"source {os.path.join(self.venv_path, 'bin/activate')} && {venv_pip} install {requirement} --cache-dir {self.cache_path}"
            else:
                pip_cmd = f"source {os.path.join(self.venv_path, 'bin/activate')} && pip install {requirement} --cache-dir {self.cache_path}"
            output, has_error = self.execute_in_subprocess(pip_cmd)
            outputs.append(output)
            if has_error:
                has_error = True
                break
            # 兼容 -p 参数（有些 pip 版本不支持 -p，通常用 -t），但按要求用 -p
            self.logger.info(f"Installing requirement with cmd: {pip_cmd}")
            
        if not has_error:
            
            self.client.add_lib_path(self.lib_path)
        return outputs, has_error

    def init_venv(self) -> bool:
        if os.path.exists(self.venv_path):
            return True
        try:
            outputs, has_err = self.execute_in_subprocess(f"python -m venv {self.venv_path}")
            if has_err:
                shutil.rmtree(self.venv_path)
                self.logger.error(f"Init venv error: {outputs}")
                raise RuntimeError(f"Init venv error: {outputs}")
            if os.path.exists(os.path.join(self.venv_path, "bin/activate")):
                self.logger.info(f"Init venv success")
                return True
            self.logger.error(f"Init venv error: {outputs}")
            shutil.rmtree(self.venv_path)
            return False
        except Exception as e:
            self.logger.error(f"Init venv error: {e}")
            shutil.rmtree(self.venv_path)
            return False
    
    def execute_in_subprocess(self, cmd: str, timeout: int = None) -> Tuple[Dict[str, Any], bool]:
        try:
            result = subprocess.run(
                cmd,
                shell=True,
                executable="/bin/bash",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=300
            )
            output = {
                "cmd": cmd,
                "returncode": result.returncode,
                "stdout": result.stdout.decode("utf-8"),
                "stderr": result.stderr.decode("utf-8"),
            }
            if result.returncode != 0:
                return output, True
            else:
                return output, False
        except Exception as e:
            output = {
                "cmd": cmd,
                "error": str(e)
            }
            return output, True
    
    def execute(self, code: str, timeout: int = None) -> Tuple[List[Dict[str, Any]], bool]:
        self.logger.info(f"Ipython kernel execute: {code}")
        return self.client.execute(code, timeout)
    
    def shutdown(self):
        self.logger.info(f"Shutdown ipython kernel")
        self.client.shutdown()