# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Python-based Data Agent system built with FastAPI that provides intelligent data analysis capabilities including NL2SQL (Natural Language to SQL), code generation, and Jupyter notebook integration. The system uses Ray for distributed computing and supports multiple AI services through MCP (Model Context Protocol).

## Architecture

### Core Components

- **FastAPI Gateway** (`infra/server/gw.py`): Main entry point handling HTTP requests and routing
- **Data Science Agent** (`infra/datascience_agent/`): Core AI agent with planning, execution, and tool capabilities
- **MCP Integration** (`infra/mcp/`): Model Context Protocol servers for different AI services
  - **NL2SQL**: Natural language to SQL conversion
  - **CodeGen**: Code generation and execution
  - **AISearch**: AI-powered search capabilities
  - **GenAD**: Advertisement generation
- **Memory System** (`infra/memory/`): Elasticsearch-based memory for chat history and context
- **Jupyter Integration** (`infra/jupyter/`): Notebook execution environment
- **Knowledge Base** (`infra/knowledge_base/`): Document processing and retrieval

### Key Design Patterns

- **Domain-Driven Design**: Clear separation between domain entities, ports, and adapters
- **Ray Actors**: Distributed computing for scalable agent execution
- **MCP Architecture**: Modular AI service integration
- **FastAPI + Pydantic**: Type-safe API development

## Development Commands

### Running the Application

```bash
# Start the main gateway server
python infra/server/gw.py

# Build Docker image
sh docker/build.sh

# Run with Docker (after build)
docker run -p 8791:8791 ccr.ccs.tencentyun.com/tsf_100041873387/data-agent:<tag>
```

### Testing

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/adapter/
pytest tests/agent/
pytest tests/server/

# Run with coverage
pytest --cov=infra
```

### Dependencies

```bash
# Install dependencies
pip install -r requirements.txt

# Using uv (recommended)
uv sync
```

## Configuration

The application uses a hierarchical configuration system:

1. **YAML Configuration**: Primary config loaded from `etc/config.yaml.tpl`
2. **Environment Variables**: Override YAML settings with `__` nested delimiter
3. **Default Values**: Fallback values defined in Pydantic models

Key configuration sections:
- `common`: Core settings (LLM, database, Redis, Ray)
- `memory`: Elasticsearch and vector store settings
- `automic`: AI service configurations (NL2SQL, CodeGen, etc.)
- `observe`: Observability (tracing, metrics)
- `guardrails`: Content safety and moderation

## Environment Variables

Required environment variables:
- `CONFIG_PATH`: Path to configuration YAML file
- `EXECUTION_MODE`: Set to "ray" for distributed execution
- `IMAGE_TAG`: Docker image tag for Ray deployment
- `LOGGER_TO_STDOUT`: Enable console logging

Optional for observability:
- `OTEL_EXPORTER_OTLP_ENDPOINT`: OpenTelemetry endpoint
- `OTEL_EXPORTER_OTLP_HEADERS`: OpenTelemetry headers
- `LANGSMITH_TRACING`: Enable LangSmith tracing

## API Structure

### Main Endpoints

- `POST /chat`: Primary chat interface with streaming support
- `POST /continue`: Continue existing chat sessions
- `POST /chat/stop_stream`: Stop active chat streams
- `POST /nl2sql`: Natural language to SQL conversion
- `POST /select_tables`: Schema linking for NL2SQL
- `POST /update_user_info`: User management operations
- `POST /query_user_session_list`: List user sessions
- `POST /query_user_session_info`: Get session details

### Request/Response Models

All API requests use Pydantic models for validation:
- `ChatConfig`: Main chat request parameters
- `NL2SQLConfig`: SQL generation parameters
- `SessionConfig`: Session management parameters

## Agent System

The data science agent follows a multi-agent architecture:

1. **Intent Recognizer**: Identifies user intent
2. **Planner**: Creates execution plans
3. **Executor**: Runs tools and code
4. **Tools**: Specialized capabilities (code generation, SQL execution, etc.)

### Agent Lifecycle

1. Agent instances are created as Ray actors for each user session
2. Agents maintain state and context throughout conversations
3. Agents can be stopped via `/chat/stop_stream` endpoint
4. Agent execution is distributed across Ray cluster nodes

## Memory and Context

### Chat Memory

- **Elasticsearch**: Stores chat history with vector embeddings
- **Redis**: Caches frequently accessed data
- **Context Management**: Maintains conversation state across requests

### Jupyter Integration

- **Notebook Execution**: Remote Jupyter kernel management
- **Code Persistence**: Stores executed code and outputs
- **Session Management**: Tracks notebook state across interactions

## Monitoring and Observability

### Metrics

- **Prometheus**: Exposes metrics at `/metrics` endpoint
- **Custom Metrics**: Code generation success rates, token usage, etc.
- **Replay Data**: Collects execution data for analysis

### Tracing

- **OpenTelemetry**: Distributed tracing support
- **LangSmith**: LLM execution tracing
- **Custom Trace Decorator**: `@traceable()` for function tracing

### Logging

- **Structured Logging**: JSON-formatted logs with context
- **Concurrent Log Handler**: Thread-safe logging
- **Request Correlation**: Trace IDs across all logs

## Development Guidelines

### Code Organization

- Follow domain-driven design principles
- Keep business logic in `infra/` directory
- Use adapters for external service integration
- Maintain clear separation between entities and ports

### Testing

- Write unit tests for all new functionality
- Use pytest fixtures for test setup
- Mock external dependencies in tests
- Test both success and error scenarios

### Error Handling

- Use consistent error response format
- Include trace IDs in error responses
- Log errors with full context
- Handle external service failures gracefully

### Performance

- Use Ray for distributed computing
- Implement connection pooling for databases
- Cache frequently accessed data
- Monitor memory usage in long-running agents

## Deployment

### Docker

The application is containerized with:
- Multi-stage builds for optimization
- Health checks for monitoring
- Environment-based configuration
- Ray cluster support for scaling

### Ray Cluster

- Supports both local and cluster deployment
- Automatic dependency management
- Distributed actor execution
- Resource allocation and monitoring