ALTER TABLE knowledge_list
    ADD COLUMN knowledge_base_name <PERSON><PERSON><PERSON><PERSON>(255) comment '知识库名称';

ALTER TABLE user_agent_versions
    ADD COLUMN app_id VARCHAR(20) comment '主用户 ID';

ALTER TABLE user_info
    ADD COLUMN app_id VARCHAR(20) comment '主用户 ID';

ALTER TABLE user_session
    ADD COLUMN app_id VARCHAR(20) comment '主用户 ID';

ALTER TABLE user_session DROP PRIMARY KEY;
UPDATE user_session SET app_id = '' WHERE app_id IS NULL;
ALTER TABLE user_session ADD PRIMARY KEY (app_id, sub_account_uin, session_id);

ALTER TABLE user_agent_versions DROP PRIMARY KEY;
UPDATE user_agent_versions SET app_id = '' WHERE app_id IS NULL;
ALTER TABLE user_agent_versions ADD PRIMARY KEY (app_id, sub_account_uin, agent_id);
