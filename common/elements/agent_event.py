import json
from typing import Any, Dict, Optional, List
from common.share.error import ErrorCode, error_msg, error_name


class AgentEvent:
    event_type: str = "event"
    metadata:Optional[Dict[str, Any]] = None
    content: Any = None

    def to_dict(self) -> dict:
        """Convert the event to a dictionary representation."""
        body_dict = {k: v for k, v in self.__dict__.items() if not k.startswith("_")}
        if 'event_type' in body_dict and body_dict['event_type'] == self.event_type:
            del body_dict['event_type']
        return body_dict

    def to_content(self) -> str:
        body: Any
        if hasattr(self, "content") and self.content is not None:
            body = self.content
        else:
            body_dict = {k: v for k, v in self.__dict__.items() if not k.startswith("_")}
            if 'event_type' in body_dict and body_dict['event_type'] == self.event_type:
                del body_dict['event_type']
            body = body_dict
        return body if isinstance(body, str) else json.dumps(body, ensure_ascii=False)

    def to_sse_format(self) -> str:
        content_val = self.to_content()
        data_str = "{}" if content_val == "{}" else json.dumps({'v': content_val}, ensure_ascii=False)
        return f"event: {self.event_type}\ndata: {data_str}\n\n"

    def __str__(self) -> str:
        return self.to_sse_format()

class MessageEvent(AgentEvent):
    event_type = "message"
    def __init__(self, content: str):
        super().__init__() # Ensure base class init if it does anything
        self.content = content

class TextEvent(AgentEvent):
    event_type = "text"
    def __init__(self, content: str = "{}"):
        super().__init__()
        self.content = content

class ThinkEvent(AgentEvent):
    event_type = "think"
    def __init__(self, content: str):
        super().__init__()
        self.content = content

class TaskListEvent(AgentEvent):
    event_type = "task_list"
    def __init__(self, content: dict): # content is the dict for the task list
        super().__init__()
        self.content = content
    def to_sse_format(self) -> str:
        # Directly use self.content as the data payload, which is {"task_list": [...]}
        if hasattr(self, 'content') and isinstance(self.content, dict):
            data_str = json.dumps(self.content, ensure_ascii=False)
        else:
            data_str = json.dumps({"error": "TaskListEvent content not found or not a dict"}, ensure_ascii=False)
        return f"event: {self.event_type}\ndata: {data_str}\n\n"


class FinishEvent(AgentEvent):
    event_type = "finish"
    def __init__(self, content: str = {}):
        super().__init__()
        self.content = content

class CloseEvent(AgentEvent):
    event_type = "close"
    def __init__(self, content: str = {}):
        super().__init__()
        self.content = content

class ErrorEvent(AgentEvent):
    event_type = "error"
    def __init__(self, err_code: ErrorCode, err_message: str = None, err_name: str = None):
        super().__init__()
        self.err_code = err_code  
        self.err_message = err_message if err_message else error_msg(err_code)
        self.err_name = err_name if err_name else error_name(err_code)
        self.content = {"err_code": self.err_code._value_, "err_message": self.err_message, "err_name": self.err_name}

    def to_sse_format(self) -> str:
        data_str = "{}" if self.content == None else json.dumps(self.content, ensure_ascii=False)
        return f"event: {self.event_type}\ndata: {data_str}\n\n"

class TitleEvent(AgentEvent):
    event_type = "title"

    def __init__(self, content: str):
        super().__init__()
        self.content = content


class RecordEvent(AgentEvent):
    event_type = "record_meta"

    def __init__(self, content: str):
        super().__init__()
        self.content = content

        
# agent 内部使用，用于记录引用信息， 会被包装成TaskListEvent 发送给前端， 后续和TaskListEvent 合并
class ReferenceEvent(AgentEvent):
    event_type = "reference"
    def __init__(self, source: str = "knowledge", status: str = None, name: str = None, count: int = None, reference_list: list = None):
        super().__init__()
        self.source = source
        self.status = status  # "running", "success", "error"
        self.name = name
        self.count = count
        self.reference_list = reference_list or []
        
        # 构建content
        content = {"source": self.source}
        if self.status:
            content["status"] = self.status
        if self.name:
            content["name"] = self.name
        if self.count is not None:
            content["count"] = self.count
        if self.reference_list:
            content["reference_list"] = self.reference_list
            
        self.content = content
    
    def to_sse_format(self) -> str:
        data_str = json.dumps(self.content, ensure_ascii=False) if self.content else "{}"
        return f"event: {self.event_type}\ndata: {data_str}\n\n"


class FinalSummaryEvent(AgentEvent):
    event_type: str = "final_summary"
    def __init__(self, content: str, cell_id: str = None):
        super().__init__()
        self.content = content
        self.cell_id = cell_id
    
    def to_dict(self) -> dict:
        return {
            "v": self.content,
            "cell_id": self.cell_id,
        }
    
    def to_str(self) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False)
    
    def to_sse_format(self) -> str:
        return f"event: {self.event_type}\ndata: {self.to_str()}\n\n"



class JupyterEvent(AgentEvent):
    """Notebook 单元执行信息专用事件"""
    event_type = "studio_jupyter"

    def set_ctx_info(self, sid: str, rid: str, tid: str):
        self.sid = sid
        self.rid = rid
        self.tid = tid

    def __init__(
        self,
        cell_type: str,                    # "code" | "sql" | "markdown" | "raw"
        execution_count: int = 0,
        source: List[str] = None,
        outputs: List[Any] = None,
        cell_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        super().__init__()
        self.cell_type = cell_type
        self.execution_count = execution_count
        self.id = cell_id
        self.metadata = metadata
        # 保持原始值，不自动补全为空数组
        self.source = source
        self.outputs = outputs
        self.set_ctx_info("sid", "rid", "tid")
    
    def to_dict(self) -> dict:
        result = {
            "v":{
                "cell_type": self.cell_type,
                "execution_count": self.execution_count,
                "id": self.id,
                "metadata": self.metadata
            },
            "sid": self.sid,
            "rid": self.rid,
            "tid": self.tid
        }
        
        # 只有当字段有实际内容时才包含（过滤掉None、空数组、空字符串等）
        if self.source:
            result["v"]["source"] = self.source
        if self.outputs:
            result["v"]["outputs"] = self.outputs
            
        return result
    
    def to_str(self) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False)
    
    def to_sse_format(self) -> str:
        return f"event: {self.event_type}\ndata: {self.to_str()}\n\n"

    def update_outputs(self, new_outputs: List[Any]) -> None:
        """仅更新outputs字段"""
        self.outputs = new_outputs
